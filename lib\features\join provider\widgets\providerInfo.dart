import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/core/widgets/image.dart';
import 'package:renvo/features/join%20provider/controller.dart';
import 'package:renvo/gen/assets.gen.dart';

class ProviderInfoPage extends StatelessWidget {
  const ProviderInfoPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<JoinProviderPageController>();

    Future<void> selectTime(BuildContext context, bool isFrom) async {
      final TimeOfDay? time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );
      if (time != null) controller.setTime(isFrom, time);
    }

    return Scaffold(
        appBar: AppBar(
          backgroundColor: StyleRepo.white,
          elevation: 0,
          leading: IconButton(
            onPressed: () => Get.back(),
            icon: Assets.icons.back.svg(color: StyleRepo.black),
            iconSize: 24,
          ),
          title: Center(
            child: Text(tr(LocaleKeys.Join_as_Services),
                style: Theme.of(context).textTheme.titleSmall),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: Form(
              key: controller.formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 20,
                  ),
                  Center(
                    child: FormField<String>(
                      initialValue: controller.image.value,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return tr(LocaleKeys.The_image_is_required);
                        }
                        return null;
                      },
                      builder: (state) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            InkWell(
                              onTap: () async {
                                await controller.pickImage();
                                state.didChange(controller.image.value);
                              },
                              child: Obx(() => controller.image.isEmpty
                                  ? Container(
                                      height: 100,
                                      width: 100,
                                      decoration: BoxDecoration(
                                        color: StyleRepo.lightGrey2,
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: state.hasError
                                              ? Theme.of(context)
                                                  .colorScheme
                                                  .error
                                              : StyleRepo.white,
                                        ),
                                      ),
                                      alignment: Alignment.center,
                                      child: Assets.icons.servicecam.svg(
                                        height: 70,
                                        width: 70,
                                      ),
                                    )
                                  : AppImage(
                                      path: controller.image.value,
                                      type: ImageType.File,
                                      height: 80,
                                      width: 80,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(),
                                      ),
                                    )),
                            ),
                            if (state.hasError) SizedBox(height: 8),
                            if (state.hasError)
                              Text(state.errorText!,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall!
                                      .copyWith(
                                        color:
                                            Theme.of(context).colorScheme.error,
                                      )),
                          ],
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 22),
                  Text(tr(LocaleKeys.Name),
                      style: Theme.of(context).textTheme.bodyMedium),
                  const SizedBox(height: 6),
                  TextFormField(
                    controller: controller.name,
                    decoration: InputDecoration(
                      prefixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(width: 8),
                          Assets.icons.circle.svg(),
                          const SizedBox(width: 8),
                          Assets.icons.line.svg(),
                          const SizedBox(width: 8),
                        ],
                      ),
                      hintText: tr(LocaleKeys.User_name),
                    ),
                    validator: (value) {
                      if (value!.isEmpty) {
                        return tr(LocaleKeys.This_field_is_required);
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 15),
                  Text(tr(LocaleKeys.Email),
                      style: Theme.of(context).textTheme.bodyMedium),
                  const SizedBox(height: 6),
                  TextFormField(
                    controller: controller.email,
                    decoration: InputDecoration(
                      prefixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(width: 8),
                          Assets.icons.message.svg(),
                          const SizedBox(width: 8),
                          Assets.icons.line.svg(),
                          const SizedBox(width: 8),
                        ],
                      ),
                      hintText: tr(LocaleKeys.user_name_gmail),
                    ),
                    validator: (value) {
                      if (value!.isEmpty) {
                        return tr(LocaleKeys.This_field_is_required);
                      }
                      if (!value.contains("@")) {
                        return tr(LocaleKeys.Wrong_email);
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 15),
                  Text(tr(LocaleKeys.Phone_Number),
                      style: Theme.of(context).textTheme.bodyMedium),
                  const SizedBox(height: 6),
                  TextFormField(
                    controller: controller.phoneNumber,
                    decoration: InputDecoration(
                      prefixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(width: 8),
                          Assets.icons.national.svg(),
                          const SizedBox(width: 8),
                          Assets.icons.line.svg(),
                          const SizedBox(width: 8),
                        ],
                      ),
                      hintText: tr(LocaleKeys.Ex),
                    ),
                  ),
                  const SizedBox(height: 15),
                  Text(tr(LocaleKeys.Gender),
                      style: Theme.of(context).textTheme.bodyMedium),
                  const SizedBox(height: 6),
                  DropdownButtonFormField<String>(
                    style: Theme.of(context).textTheme.labelMedium,
                    value: controller.gender.text.isNotEmpty
                        ? controller.gender.text
                        : null,
                    onChanged: (value) {
                      controller.gender.text = value!;
                    },
                    items: [
                      DropdownMenuItem(
                        value: '1',
                        child: Text(tr(LocaleKeys.Male)),
                      ),
                      DropdownMenuItem(
                        value: '2',
                        child: Text(tr(LocaleKeys.Female)),
                      ),
                    ],
                    decoration: InputDecoration(
                      prefixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(width: 8),
                          Assets.icons.gender.svg(),
                          const SizedBox(width: 8),
                          Assets.icons.line.svg(),
                          const SizedBox(width: 8),
                        ],
                      ),
                      hintText: tr(LocaleKeys.Male),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return tr(LocaleKeys.Please_select_gender);
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 30),
                  Text(tr(LocaleKeys.Working_Time)),
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            if (!controller.anyTime.value) {
                              selectTime(context, true);
                            }
                          },
                          child: TextFormField(
                            controller: controller.fromTimeTextController,
                            enabled: false,
                            style: Theme.of(context).textTheme.bodyLarge,
                            decoration: InputDecoration(
                              hintText: tr(LocaleKeys.From),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 10),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            if (!controller.anyTime.value) {
                              selectTime(context, false);
                            }
                          },
                          child: TextFormField(
                            controller: controller.toTimeTextController,
                            enabled: false,
                            style: Theme.of(context).textTheme.bodyLarge,
                            decoration: InputDecoration(
                              hintText: tr(LocaleKeys.To),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 20,
                  ),
                  Container(
                    height: 55,
                    width: 175,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Obx(() => Row(
                          children: [
                            Checkbox(
                                value: controller.anyTime.value,
                                shape: CircleBorder(),
                                onChanged: controller.toggleAnyTime),
                            Text(tr(LocaleKeys.Any_Time)),
                          ],
                        )),
                  ),
                  SizedBox(height: 16),
                  Text(tr(LocaleKeys.Description)),
                  SizedBox(height: 10),
                  TextFormField(
                    controller: controller.descriptionController,
                    maxLines: 4,
                    decoration: InputDecoration(
                      hintText: tr(LocaleKeys.Add_Description),
                      contentPadding: EdgeInsets.all(12),
                    ),
                  ),
                  SizedBox(height: 16),
                  Text(tr(LocaleKeys.Upload_Photos)),
                  SizedBox(height: 10),
                  InkWell(
                    onTap: controller.pickImages,
                    child: Obx(() {
                      if (controller.selectedImages.isEmpty) {
                        return Container(
                            height: 200,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: StyleRepo.white,
                              border: Border.all(
                                color: StyleRepo.lightGrey,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Assets.icons.uploadPhoto
                                    .svg(width: 27, height: 27),
                                Text(
                                  tr(LocaleKeys.Upload_Photos),
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelSmall!
                                      .copyWith(color: StyleRepo.lightGrey),
                                ),
                                SizedBox(width: 11),
                              ],
                            ));
                      } else {
                        return Container(
                          height: 140,
                          child: GridView.builder(
                            scrollDirection: Axis.horizontal,
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2,
                              crossAxisSpacing: 2,
                              mainAxisSpacing: 2,
                              childAspectRatio: 1,
                            ),
                            itemCount: controller.selectedImages.length,
                            itemBuilder: (context, index) {
                              return Stack(
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      image: DecorationImage(
                                        image: FileImage(File(controller
                                            .selectedImages[index].path)),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    margin: EdgeInsets.all(4),
                                  ),
                                  Positioned(
                                    top: 2,
                                    right: 2,
                                    child: GestureDetector(
                                      onTap: () =>
                                          controller.removeImage(index),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: Colors.black54,
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(Icons.close,
                                            size: 20, color: Colors.white),
                                      ),
                                    ),
                                  )
                                ],
                              );
                            },
                          ),
                        );
                      }
                    }),
                  ),
                  SizedBox(height: 16),
                  const SizedBox(height: 16),
                  Obx(() => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Radio<bool>(
                                value: true,
                                groupValue: controller.isSpecificPlace.value,
                                activeColor: Theme.of(context).primaryColor,
                                onChanged: (val) {
                                  controller.isSpecificPlace.value = true;
                                  controller.isAllRegions.value = false;
                                },
                              ),
                              Text(
                                tr(LocaleKeys.specific_place),
                                style: TextStyle(
                                  color: controller.isSpecificPlace.value
                                      ? Theme.of(context).primaryColor
                                      : StyleRepo.gray,
                                ),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              Radio<bool>(
                                value: true,
                                groupValue: controller.isAllRegions.value,
                                activeColor: Theme.of(context).primaryColor,
                                onChanged: (val) {
                                  controller.isAllRegions.value = true;
                                  controller.isSpecificPlace.value = false;
                                },
                              ),
                              Text(
                                tr(LocaleKeys.all_regions),
                                style: TextStyle(
                                  color: controller.isAllRegions.value
                                      ? Theme.of(context).primaryColor
                                      : StyleRepo.gray,
                                ),
                              ),
                            ],
                          ),
                        ],
                      )),
                  Center(
                    child: ElevatedButton(
                      onPressed: () {
                        controller.confirm();
                      },
                      child: Text(tr(LocaleKeys.Next)),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }
}
