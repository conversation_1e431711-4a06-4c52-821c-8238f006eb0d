import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/routes/routes.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/core/widgets/svg_icon.dart';
import 'package:renvo/features/main/controller.dart';
import 'package:renvo/gen/assets.gen.dart';

class FloatingNavBar extends StatelessWidget {
  const FloatingNavBar({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MainPageController>();
    final items = [
      {'icon': SvgIcon(icon: Assets.icons.logo), 'label': tr(LocaleKeys.home)},
      {
        'icon': SvgIcon(icon: Assets.icons.myOrder),
        'label': tr(LocaleKeys.MyOrder)
      },
      {'icon': SvgIcon(icon: Assets.icons.add), 'label': tr(LocaleKeys.Add)},
      {'icon': SvgIcon(icon: Assets.icons.chat), 'label': tr(LocaleKeys.Chat)},
      {
        'icon': Image.asset(
          Assets.icons.profilepng.path,
          width: 24,
          height: 24,
        ),
        'label': tr(LocaleKeys.Profile)
      },
    ];

    return Container(
      height: 70,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        color: StyleRepo.whiteSmoke,
        borderRadius: BorderRadius.circular(24),
      ),
      child: Obx(
        () => Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: List.generate(items.length, (index) {
            final isSelected = controller.currentPage.value == index;
            final color = isSelected ? StyleRepo.blue : Colors.grey;

            return InkWell(
              onTap: () {
                if (index == 2) {
                  Get.toNamed(Pages.AddOrder.value);
                } else {
                  controller.currentPage.value = index;
                }
              },
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconTheme(
                    data: IconThemeData(color: color),
                    child: items[index]['icon'] as Widget,
                  ),
                  const SizedBox(height: 4),
                  Text(items[index]['label'] as String,
                      style: Theme.of(context)
                          .textTheme
                          .labelSmall!
                          .copyWith(color: color))
                ],
              ),
            );
          }),
        ),
      ),
    );
  }
}

// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:get/get_core/src/get_main.dart';
// import 'package:renvo/core/localization/strings.dart';
// import 'package:renvo/core/widgets/svg_icon.dart';
// import 'package:renvo/features/main/controller.dart';
// import 'package:renvo/gen/assets.gen.dart';

// class NavBar extends StatelessWidget {
//   const NavBar({
//     super.key,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final controller = Get.find<MainPageController>();
//     // MainPageController controller = Get.find();
//     return Obx(
//       () => NavigationBar(
//         onDestinationSelected: (page) => controller.currentPage.value = page,
//         selectedIndex: controller.currentPage.value,
//         destinations: [
//           NavigationDestination(
//             icon: SvgIcon(icon: Assets.icons.logo),
//             label: tr(LocaleKeys.home),
//           ),
//           NavigationDestination(
//             icon: SvgIcon(icon: Assets.icons.myOrder),
//             label: tr(LocaleKeys.MyOrder),
//           ),
//           NavigationDestination(
//             icon: SvgIcon(icon: Assets.icons.add),
//             label: tr(LocaleKeys.Add),
//           ),
//           NavigationDestination(
//             icon: SvgIcon(icon: Assets.icons.chat),
//             label: tr(LocaleKeys.Chat),
//           ),
//           NavigationDestination(
//             icon: Image.asset(
//               Assets.icons.profilepng.path,
//               width: 24, // اختياري: لتحديد الحجم
//               height: 24,
//             ),
//             label: tr(LocaleKeys.Profile),
//           ),
//         ],
//       ),
//     );
//   }
// }
