import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:image_picker/image_picker.dart';
import 'package:renvo/core/config/app_builder.dart';
import 'package:renvo/core/services/rest_api/constants/end_points.dart';
import 'package:renvo/core/services/rest_api/models/request.dart';
import 'package:renvo/core/services/rest_api/models/response_model.dart';

import '../../core/services/rest_api/api_service.dart';

class JoinProviderPageController extends GetxController {
  final controller = Get.find<AppBuilder>();
  // قائمة تحتوي على RxBool لكل كرت
  var checkboxes = <RxBool>[].obs;
  RxBool isSpecificPlace = false.obs;
  RxBool isAllRegions = false.obs;
  List<int> numbers = [12, 8];
  final fromTime = Rx<TimeOfDay?>(null);
  final toTime = Rx<TimeOfDay?>(null);

  RxBool anyTime = false.obs;
  RxList<XFile> selectedImages = <XFile>[].obs;
  List<MultipartFile> galleryImages = [];

  // إنشاء عدد معين من العناصر (مثلاً 4)
  void initializeCheckboxes(int count) {
    checkboxes.value = List.generate(count, (_) => false.obs);
  }

  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController email,
      name,
      phoneNumber,
      gender,
      fromTimeTextController,
      toTimeTextController,
      descriptionController;

  Rx<String> image = "".obs;
  pickImage() async {
    XFile? picked = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (picked == null) return;

    image.value = picked.path;
  }

  @override
  onInit() {
    initializeCheckboxes(4);
    email = TextEditingController();
    name = TextEditingController();
    phoneNumber = TextEditingController();
    gender = TextEditingController();
    descriptionController = TextEditingController();
    toTimeTextController = TextEditingController();
    fromTimeTextController = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    email.dispose();
    name.dispose();

    phoneNumber.dispose();
    gender.dispose();
    super.onClose();
  }

  void confirm() async {
    if (!formKey.currentState!.validate()) return;

    final formMap = {
      "name": name.text,
      "phone": phoneNumber.text,
      "description": descriptionController.text,
      "start_at": fromTimeTextController.text,
      "end_at": toTimeTextController.text,
      "email": email.text,
      "prv_category_ids[0]": numbers.first,
      "dial_country_code": "963",
      "avatar": await MultipartFile.fromFile(image.value),
    };

    for (int i = 0; i < galleryImages.length; i++) {
      formMap["gallery[$i]"] = galleryImages[i];
    }

    ResponseModel response = await APIService.instance.request(Request(
      endPoint: EndPoints.Join_Provider,
      method: RequestMethod.Post,
      body: FormData.fromMap(formMap),
    ));

    if (response.success) {}
  }

  void setTime(bool isFrom, TimeOfDay time) {
    if (isFrom) {
      fromTime.value = time;
      fromTimeTextController.text = time.format(Get.context!); // التحديث هنا
    } else {
      toTime.value = time;
      toTimeTextController.text = time.format(Get.context!); // التحديث هنا
    }
  }

  void toggleAnyTime(bool? value) {
    anyTime.value = value ?? false;
    if (anyTime.value) {
      fromTimeTextController.clear();
      toTimeTextController.clear();
    }
  }

  Future<void> pickImages() async {
    final picker = ImagePicker();
    final pickedFiles = await picker.pickMultiImage();

    if (pickedFiles != null) {
      final totalImages = [...selectedImages, ...pickedFiles];

      if (totalImages.length > 10) {
        selectedImages.value = totalImages.sublist(0, 10);
        Get.snackbar('تنبيه', 'يمكنك اختيار حتى 10 صور فقط');
      } else {
        selectedImages.value = totalImages;
      }
    }
    galleryImages.clear();
    for (int i = 0; i < selectedImages.length; i++) {
      galleryImages.add(await MultipartFile.fromFile(selectedImages[i].path));
    }
  }

  void removeImage(int index) {
    selectedImages.removeAt(index);
  }
}
