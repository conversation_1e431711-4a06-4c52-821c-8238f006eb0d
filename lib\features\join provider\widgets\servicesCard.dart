import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/gen/assets.gen.dart';

class ServicesCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final SvgPicture picture;
  final RxBool isChecked;

  const ServicesCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.picture,
    required this.isChecked,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 25.8, sigmaY: 25.8),
        child: Container(
          height: 105,
          width: 330,
          color: const Color(0x1AFFFFFF),
          child: Obx(
            () => Row(
              children: [
                isChecked.value
                    ? Assets.icons.linewhite.svg()
                    : const SizedBox(),
                const SizedBox(width: 10),
                picture,
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context)
                            .textTheme
                            .labelLarge!
                            .copyWith(color: StyleRepo.white),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall!
                            .copyWith(color: StyleRepo.white),
                      ),
                    ],
                  ),
                ),
                Obx(
                  () => Checkbox(
                    value: isChecked.value,
                    onChanged: (val) => isChecked.value = val!,
                    checkColor: StyleRepo.indigo,
                    activeColor: StyleRepo.white,
                    shape: const CircleBorder(), // Makes it circular
                  ),
                ),
                const SizedBox(width: 10),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
