import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/core/widgets/WidgetController.dart';
import 'package:renvo/core/widgets/svg_icon.dart';
import 'package:renvo/gen/assets.gen.dart';

import 'controller.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SplashScreenController());

    return Scaffold(
      backgroundColor: Theme.of(context).primaryColor,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 60,
                width: 60,
                decoration: BoxDecoration(
                    color: StyleRepo.white,
                    borderRadius: BorderRadius.circular(100)),
                child: Center(
                  child: GetBuilder<AppController>(
                    builder: (controller) {
                      return AnimatedBuilder(
                        animation: controller.rotationController,
                        builder: (context, child) {
                          return Transform.rotate(
                            angle: controller.rotationController.value *
                                2 *
                                3.1416,
                            child: SvgIcon(
                              icon: Assets.icons.logo,
                              color: StyleRepo.blue,
                              size: 50,
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ),
              SizedBox(
                width: 10,
              ),
              SvgIcon(
                icon: Assets.icons.renvo,
                color: StyleRepo.white,
                size: 100,
              ),
            ],
          ),
          Text(
            "Recruitment,assistance and cooperation",
            style: TextStyle(color: StyleRepo.white, fontSize: 8),
          )
        ],
      ),
    );
  }
}
