import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:renvo/core/config/app_builder.dart';
import 'package:renvo/core/config/role.dart';
import 'package:renvo/core/routes/routes.dart';

import '../../../core/services/rest_api/rest_api.dart';

class ResendPassPageController extends GetxController {
  AppBuilder appBuilder = Get.find();
  var passwordRegex = RegExp(
    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$',
  );
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late String phone;
  late TextEditingController password, confirmpassword;

  @override
  onInit() {
    password = TextEditingController();
    confirmpassword = TextEditingController();
    phone = Get.arguments;
    super.onInit();
  }

  @override
  onClose() {
    confirmpassword.dispose();
    password.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.ResendPass,
        method: RequestMethod.Post,
        body: {
          "password": password.text,
          "password_confirmation": confirmpassword.text
        },
      ),
    );

    if (response.success) {
      if (response.data[0].toString().contains("Updated Successfully")) {
        Get.toNamed(Pages.home.value);
        Get.snackbar("password changed success", "please change pass ");
      } else {
        Get.snackbar("Errore pass", "please change pass  ");
      }
    }
  }
}
