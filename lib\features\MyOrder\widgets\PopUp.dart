import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/gen/assets.gen.dart';

class Popup extends StatelessWidget {
  final Widget icon;
  const Popup({super.key, required this.icon});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: icon,
      onPressed: () async {
        final RenderBox button = context.findRenderObject() as RenderBox;
        final RenderBox overlay =
            Overlay.of(context).context.findRenderObject() as RenderBox;
        final Offset offset =
            button.localToGlobal(Offset.zero, ancestor: overlay);

        // اجعل القائمة تظهر أسفل الأيقونة
        final RelativeRect position = RelativeRect.fromLTRB(
          offset.dx,
          offset.dy + 40,
          0,
          0,
        );

        showMenu(
          color: StyleRepo.white,
          context: context,
          position: position,
          items: [
            PopupMenuItem(
              child: Row(
                children: [
                  Assets.icons.eye.svg(),
                  const SizedBox(width: 4),
                  Text(
                    tr(LocaleKeys.View_details),
                    style: Theme.of(context).textTheme.labelMedium,
                  ),
                ],
              ),
            ),
            PopupMenuItem(
              child: Row(
                children: [
                  Assets.icons.cancel.svg(),
                  const SizedBox(width: 4),
                  Text(
                    tr(LocaleKeys.Cancel_service),
                    style: Theme.of(context)
                        .textTheme
                        .labelMedium!
                        .copyWith(color: StyleRepo.red),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
