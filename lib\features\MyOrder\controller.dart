import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/config/StatusOrder.dart';

class MyOrderPageController extends GetxController
    with GetTickerProviderStateMixin {
  late TabController tabController;
  var rating = 0.obs;

  void setRating(int value) {
    rating.value = value;
  }

  @override
  void onInit() {
    tabController =
        TabController(length: StatusOrder.values.length, vsync: this);
    super.onInit();
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }
}
