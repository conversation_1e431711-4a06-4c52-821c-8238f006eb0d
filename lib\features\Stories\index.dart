import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/core/widgets/image.dart';
import 'package:renvo/features/Stories/controller.dart';
import 'package:renvo/gen/assets.gen.dart';

class StoriesPage extends StatelessWidget {
  StoriesPage({super.key});
  final controller = Get.put(StoriesPageController());
  @override
  Widget build(BuildContext context) {
    final screenheight = MediaQuery.of(context).size.height;
    final screenwidth = MediaQuery.of(context).size.width;
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        CarouselSlider(
          options: CarouselOptions(
            height: double.infinity,
            autoPlay: false,
            viewportFraction: 1.0,
            onPageChanged: (index, _) => controller.onPageChanged(index),
            enlargeCenterPage: false,
          ),
          items: controller.ads.map((url) {
            return AppImage(
              path: url,
              width: double.infinity,
              height: double.infinity,
              type: ImageType.CachedNetwork,
              fit: BoxFit.cover,
            );
          }).toList(),
          carouselController: controller.carouselController,
        ),
        Positioned(
          top: screenheight * .1,
          left: 10,
          right: 10,
          child: Obx(() => Row(
                children: List.generate(controller.ads.length, (index) {
                  bool isActive = controller.currentAd.value == index;
                  bool isPassed = index < controller.currentAd.value;

                  return Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 2.0),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(3),
                        child: LinearProgressIndicator(
                          value: isPassed
                              ? 1.0
                              : isActive
                                  ? controller.progress.value
                                  : 0.0,
                          backgroundColor: Colors.white,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(StyleRepo.blue),
                          minHeight: 4,
                        ),
                      ),
                    ),
                  );
                }),
              )),
        ),

        // Positioned(
        //   top: screenheight * .1,
        //   child: Obx(() => Row(
        //         mainAxisAlignment: MainAxisAlignment.center,
        //         children: List.generate(
        //           ads.length,
        //           (index) => Container(
        //             margin: const EdgeInsets.symmetric(horizontal: 3),
        //             width: (MediaQuery.of(context).size.width / 3) - 20,
        //             height: 6,
        //             decoration: BoxDecoration(
        //               borderRadius: BorderRadius.circular(2),
        //               color: _currentAd.value == index
        //                   ? StyleRepo.blue
        //                   : StyleRepo.white,
        //             ),
        //           ),
        //         ),
        //       )),
        // ),
        Positioned(
            top: screenheight * .04,
            left: screenwidth * .02,
            child: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: Row(
                children: [
                  Assets.icons.back.svg(),
                  SizedBox(
                    width: 6,
                  ),
                  Text(
                    tr(LocaleKeys.Back),
                    style: Theme.of(context)
                        .textTheme
                        .titleSmall!
                        .copyWith(color: StyleRepo.white),
                  ),
                ],
              ),
            )),
        Positioned(
          bottom: screenheight * .04,
          right: screenwidth * .04,
          left: screenwidth * .04,
          child: SizedBox(
            width: MediaQuery.of(context).size.width,
            //height: 500,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Assets.icons.love.svg(height: 44, width: 44),
                SizedBox(
                  height: 18,
                ),
                Assets.icons.addstory.svg(height: 44, width: 44),
                SizedBox(
                  height: 18,
                ),
                Assets.icons.share.svg(height: 44, width: 44),
                SizedBox(
                  height: 18,
                ),
                Row(
                  children: [
                    AppImage(
                      path: Assets.icons.profilepng.path,
                      type: ImageType.Asset,
                      height: 52,
                      width: 52,
                    ),
                    SizedBox(
                      width: 11,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Provider Name",
                            style: Theme.of(context)
                                .textTheme
                                .titleSmall!
                                .copyWith(color: StyleRepo.white),
                          ),
                          Text(
                            "provider category",
                            style: Theme.of(context)
                                .textTheme
                                .labelSmall!
                                .copyWith(color: StyleRepo.white),
                          ),
                          Row(
                            children: List.generate(
                              5,
                              (index) {
                                return Icon(
                                  Icons.star,
                                  color: StyleRepo.sandyBrown,
                                  size: 24,
                                );
                              },
                            ),
                          )
                        ],
                      ),
                    ),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: StyleRepo.transparent,
                        foregroundColor: StyleRepo.white,
                        side: BorderSide(color: StyleRepo.lightGrey),
                        fixedSize: Size(140, 34),
                        textStyle: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      onPressed: () {},
                      child: Text(
                        tr(LocaleKeys.ViewProfile),
                        style: Theme.of(context)
                            .textTheme
                            .labelMedium!
                            .copyWith(color: StyleRepo.white),
                      ),
                    )
                  ],
                )
              ],
            ),
          ),
        )
      ],
    );
  }
}
