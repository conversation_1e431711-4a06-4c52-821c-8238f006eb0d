import 'package:easy_localization/easy_localization.dart';
import 'package:renvo/core/localization/strings.dart';

enum StatusOrder {
  Pending,
  Underway,
  Complete,
  Cancelled;

  String get trValue {
    switch (this) {
      case StatusOrder.Pending:
        return tr(LocaleKeys.Pending);
      case StatusOrder.Underway:
        return tr(LocaleKeys.Underway);
      case StatusOrder.Complete:
        return tr(LocaleKeys.Complete);
      case StatusOrder.Cancelled:
        return tr(LocaleKeys.Cancelled);
    }
  }
}
