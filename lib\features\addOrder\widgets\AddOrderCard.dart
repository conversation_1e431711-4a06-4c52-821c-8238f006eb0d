import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/gen/assets.gen.dart';

class AddOrderCard extends StatelessWidget {
  final Widget CardOrder;
  const AddOrderCard({super.key, required this.CardOrder});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Color(0x40000000),
            blurRadius: 4,
            offset: Offset(0, 4),
          ),
        ],
        gradient: LinearGradient(
          colors: [
            StyleRepo.blue,
            StyleRepo.blue,
            StyleRepo.whiteblue,
            StyleRepo.whiteblue,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          Positioned(
            top: 50,
            left: 10,
            child: IconButton(
              onPressed: () => Get.back(),
              icon: Assets.icons.back.svg(color: StyleRepo.white),
              iconSize: 24,
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * .08,
            left: 0,
            right: 0,
            child: Assets.icons.renva.svg(width: 350, height: 92),
          ),
          Positioned(top: 92, right: 0, left: 0, child: CardOrder),
        ],
      ),
    );
  }
}
