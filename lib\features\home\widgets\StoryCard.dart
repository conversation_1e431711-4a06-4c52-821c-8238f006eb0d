import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/demo/media.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/core/widgets/image.dart';

class StoryCard extends StatelessWidget {
  StoryCard({super.key});

  final RxInt _currentAd = 0.obs;

  final List ads = [
    DemoMedia.getAppRandomImage,
    DemoMedia.getAppRandomImage,
    DemoMedia.getAppRandomImage,
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 160, // حسب ما يناسب التصميم، ممكن 160-180
      margin: const EdgeInsets.only(right: 12),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: SizedBox(
            height: 250,
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                CarouselSlider(
                  options: CarouselOptions(
                    height: double.infinity,
                    autoPlay: true,
                    viewportFraction: 1.0,
                    onPageChanged: (index, _) => _currentAd.value = index,
                    enlargeCenterPage: false,
                  ),
                  items: ads.map((url) {
                    return AppImage(
                      path: url,
                      width: double.infinity,
                      height: double.infinity,
                      type: ImageType.CachedNetwork,
                      fit: BoxFit.cover,
                    );
                  }).toList(),
                ),
                Positioned(
                  top: 8,
                  child: Obx(() => Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          ads.length,
                          (index) => Container(
                            margin: const EdgeInsets.symmetric(horizontal: 3),
                            width: 35,
                            height: 2,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(2),
                              color: _currentAd.value == index
                                  ? StyleRepo.white
                                  : StyleRepo.lightGrey,
                            ),
                          ),
                        ),
                      )),
                ),
              ],
            ),
          )),
    );
  }
}
