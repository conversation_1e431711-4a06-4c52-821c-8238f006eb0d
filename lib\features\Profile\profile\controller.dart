import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:renvo/core/services/rest_api/rest_api.dart';
import 'package:renvo/core/services/state_management/obs.dart';
import 'package:renvo/features/Profile/profile/model/ProfileModel.dart';
import 'package:renvo/features/home/<USER>';

class ProfilePageController extends GetxController {
  ObsVar<ProfileModel> Profile = ObsVar(null);

  void fetchProfile() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.Profile,
        fromJson: ProfileModel.fromJson,
      ),
    );

    if (response.success) {
      Profile.value = response.data;
    } else {
      Profile.error = response.message;
    }
  }

  Rx<String> image = "".obs;

  pickImage() async {
    XFile? picked = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (picked == null) return;

    image.value = picked.path;
  }

  @override
  onInit() {
    fetchProfile();
    super.onInit();
  }
}
