import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AppController extends GetxController
    with GetSingleTickerProviderStateMixin {
  late AnimationController rotationController;

  @override
  void onInit() {
    super.onInit();
    rotationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat(); // الدوران المستمر
  }

  @override
  void onClose() {
    rotationController.dispose();
    super.onClose();
  }
}

class LogoController extends GetxController {
  var scrollOffset = 0.0.obs;

  void updateScroll(double offset) {
    scrollOffset.value = offset;
  }
}
