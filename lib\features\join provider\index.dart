import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/routes/routes.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/features/addOrder/widgets/CardOrder.dart';
import 'package:renvo/features/join%20provider/controller.dart';
import 'package:renvo/features/join%20provider/widgets/servicesCard.dart';
import 'package:renvo/gen/assets.gen.dart';

class JoinProviderPage extends StatelessWidget {
  const JoinProviderPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(JoinProviderPageController());
    return Scaffold(
      appBar: AppBar(
        backgroundColor: StyleRepo.blue,
        leading: IconButton(
          onPressed: () => Get.back(),
          icon: Assets.icons.back.svg(color: StyleRepo.white),
          iconSize: 24,
        ),
      ),
      body: Container(
          padding: EdgeInsets.all(30),
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Color(0x40000000),
                blurRadius: 4,
                offset: Offset(0, 4),
              ),
            ],
            gradient: LinearGradient(
              colors: [
                StyleRepo.blue,
                StyleRepo.whiteblue,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "Select Services Type",
                style: Theme.of(context)
                    .textTheme
                    .headlineSmall!
                    .copyWith(color: StyleRepo.white),
              ),
              SizedBox(
                height: 21,
              ),
              Center(
                child: Text(
                  "Welcome to Renva ... Join us as a service provider and get all the features of the app",
                  style: Theme.of(context)
                      .textTheme
                      .labelSmall!
                      .copyWith(color: StyleRepo.white),
                ),
              ),
              SizedBox(
                height: 21,
              ),
              ServicesCard(
                title: tr(LocaleKeys.Household_Services),
                subtitle: tr(LocaleKeys.Cleaning_Ironing),
                picture: Assets.icons.houseServices.svg(),
                isChecked: controller.checkboxes[0],
              ),
              SizedBox(
                height: 10,
              ),
              ServicesCard(
                title: tr(LocaleKeys.Professional_Services),
                subtitle: tr(LocaleKeys.Electrical_Plumbing),
                picture: Assets.icons.professionalServices.svg(),
                isChecked: controller.checkboxes[1],
              ),
              SizedBox(
                height: 10,
              ),
              ServicesCard(
                title: tr(LocaleKeys.Personal_Services),
                subtitle: tr(LocaleKeys.Personal_Training),
                picture: Assets.icons.personalServices.svg(),
                isChecked: controller.checkboxes[2],
              ),
              SizedBox(
                height: 10,
              ),
              ServicesCard(
                title: tr(LocaleKeys.Logistical_Services),
                subtitle: tr(LocaleKeys.Transport_Deliveries),
                picture: Assets.icons.logisticalServices.svg(),
                isChecked: controller.checkboxes[3],
              ),
              SizedBox(
                height: 65,
              ),
              ElevatedButton(
                onPressed: () => Get.toNamed(Pages.ProviderInfo.value),
                child: Text("Next",
                    style: Theme.of(context)
                        .textTheme
                        .labelLarge!
                        .copyWith(color: StyleRepo.blue)),
                style: ButtonStyle(
                  backgroundColor: WidgetStatePropertyAll(StyleRepo.white),
                ),
              )
            ],
          )),
    );
  }
}
