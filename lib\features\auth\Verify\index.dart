import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/routes/routes.dart';
import 'package:renvo/core/widgets/auth_card.dart';
import 'package:renvo/gen/assets.gen.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import 'controller.dart';

class VerifyPage extends StatelessWidget {
  const VerifyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(VerifyPageController());
    return AuthCard(
      leading: true,
      card: Form(
        key: controller.formKey,
        child: ListView(padding: EdgeInsets.all(15), children: [
          SizedBox(height: MediaQuery.sizeOf(context).height * .02),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(tr(LocaleKeys.Verify_Phone_Number),
                  style: Theme.of(context).textTheme.headlineSmall),
              SizedBox(
                width: 10,
              ),
              Assets.icons.veify.svg()
            ],
          ),
          SizedBox(
            width: 20,
          ),
          Text(tr(LocaleKeys.Enter_the_Code),
              style: Theme.of(context).textTheme.bodyMedium),
          SizedBox(
            height: 40,
          ),
          PinCodeTextField(
            controller: controller.otp,
            appContext: context,
            length: 4,
            obscureText: false,
            animationType: AnimationType.fade,
            keyboardType: TextInputType.number,
            autovalidateMode: AutovalidateMode.disabled, // ⛔ لا تتحقق تلقائيًا
            validator: (value) {
              if (value!.isEmpty || value.length != 4) {
                return tr(LocaleKeys.Passwords_do_not_match);
              }
            },
            pinTheme: PinTheme(
              shape: PinCodeFieldShape.box,
              borderRadius: BorderRadius.circular(15),
              fieldHeight: 60,
              fieldWidth: 60,
              activeColor: Colors.green,
              selectedColor: Colors.green,
              inactiveColor: Colors.green,
              activeFillColor: Colors.green[100]!,
              selectedFillColor: Colors.green[100]!,
              inactiveFillColor: Colors.green[100]!,
            ),
            animationDuration: Duration(milliseconds: 300),
            enableActiveFill: true,
            onChanged: (value) {},
            onCompleted: (value) {
              print('${tr(LocaleKeys.the_entered_code)} $value');
            },
          ),
          SizedBox(height: 20),
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Obx(() => Text(
                  controller.formattedTime + "  " + tr(LocaleKeys.second),
                  style: Theme.of(context).textTheme.labelSmall)),
              SizedBox(height: 10),
              Obx(() => GestureDetector(
                    onTap: controller.isResendActive.value
                        ? () {
                            // إعادة إرسال الكود
                            controller.startTimer();
                          }
                        : null,
                    child: Text(
                      tr(LocaleKeys.Resend_Code),
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: controller.isResendActive.value
                                ? Colors.blue
                                : Colors.grey,
                          ),
                    ),
                  )),
              SizedBox(
                height: MediaQuery.of(context).size.height * .25,
              ),
              ElevatedButton(
                onPressed: () {
                  controller.confirm();
                },
                child: Text(
                  tr(LocaleKeys.Confirm),
                ),
              ),
            ],
          )
        ]),
      ),
    );
  }
}
