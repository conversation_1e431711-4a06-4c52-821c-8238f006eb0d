import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/models/category.dart';
import 'package:renvo/core/services/state_management/widgets/obs_widget.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/features/home/<USER>';
import 'package:renvo/features/home/<USER>/CardCategories.dart';
import 'package:renvo/gen/assets.gen.dart';

class Homecategories extends StatelessWidget {
  const Homecategories({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomePageController>();

    return SliverAppBar(
      expandedHeight: 500,
      collapsedHeight: 150,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
          background: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              StyleRepo.blue,
              StyleRepo.whiteblue,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Stack(alignment: Alignment.topRight, children: [
          Assets.icons.logoHome.svg(),
          Positioned(right: -20, top: -20, child: Assets.icons.logohome2.svg()),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 60,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Assets.icons.renvo.svg(height: 27, width: 111),
                    SizedBox(
                      width: 100,
                    ),
                    Assets.icons.alarm.svg()
                  ],
                ),
                Row(
                  children: [
                    Text(tr(LocaleKeys.Your_Location),
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              color: StyleRepo.white, // أو StyleRepo.blue مثلاً
                            )),
                    SizedBox(
                      width: 10,
                    ),
                    Text(tr(LocaleKeys.Location_Name),
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              color: StyleRepo.white, // أو StyleRepo.blue مثلاً
                            )),
                    IconButton(
                        onPressed: () {},
                        icon: Icon(
                            color: StyleRepo.white,
                            Icons.arrow_downward_rounded)),
                  ],
                ),
                SizedBox(
                  height: 20,
                ),
                Expanded(
                  child: ObsListBuilder(
                    obs: controller.Category,
                    builder: (context, Category) {
                      return GridView(
                        padding: EdgeInsets.symmetric(horizontal: 2),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                          childAspectRatio: 1.2,
                        ),
                        //   shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        children: List.generate(
                          Category.length,
                          (index) => Cardcategories(
                            service: Category[index].title,
                            explain_service: Category[index].description ?? '',
                            picture: Assets.icons.houseServices
                                .svg(width: 56, height: 56),
                          ),
                        ),
                      );
                    },
                  ),
                )
              ],
            ),
          )
        ]),
      )),
    );
  }
}
