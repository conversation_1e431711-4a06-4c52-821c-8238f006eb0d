// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:renvo/core/localization/strings.dart';
// import 'package:renvo/core/routes/routes.dart';
// import 'package:renvo/core/style/repo.dart';
// import 'package:renvo/core/widgets/WidgetController.dart';
// import 'package:renvo/core/widgets/svg_icon.dart';
// import 'package:renvo/features/auth/login/controller.dart';
// import 'package:renvo/gen/assets.gen.dart';

// class AuthCardd extends StatelessWidget {
//   const AuthCardd({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final controller = Get.put(LogoController());
//     final primaryColor = Theme.of(context).primaryColor;
//     final screenHeight = MediaQuery.of(context).size.height;

//     return Scaffold(
//       backgroundColor: primaryColor,
//       body: NotificationListener<ScrollNotification>(
//         onNotification: (scrollNotification) {
//           if (scrollNotification is ScrollUpdateNotification) {
//             controller.updateScroll(scrollNotification.metrics.pixels);
//           }
//           return true;
//         },
//         child: Stack(
//           children: [
//             CustomScrollView(
//               slivers: [
//                 SliverAppBar(
//                   backgroundColor: primaryColor,
//                   expandedHeight: screenHeight * 0.3,
//                   pinned: true,
//                   flexibleSpace: Obx(() {
//                     // تحكم بالحجم حسب التمرير
//                     double offset = controller.scrollOffset.value;
//                     const minSize = 30.0;
//                     const maxSize = 80.0;
//                     double size =
//                         (maxSize - offset / 5).clamp(minSize, maxSize);

//                     return Center(
//                       child: Padding(
//                         padding: const EdgeInsets.only(bottom: 20),
//                         child: SvgIcon(
//                           icon: Assets.icons.logo,
//                           color: StyleRepo.white,
//                           size: size,
//                         ),
//                       ),
//                     );
//                   }),
//                 ),
//               ],
//             ),
//             DraggableScrollableSheet(
//               initialChildSize: 0.65,
//               minChildSize: 0.6,
//               maxChildSize: 0.9,
//               builder: (context, scrollController) {
//                 return Container(
//                   decoration: const BoxDecoration(
//                     color: StyleRepo.white,
//                     borderRadius:
//                         BorderRadius.vertical(top: Radius.circular(40)),
//                   ),
//                   child: Form(
//                     //     key: controller.formKey,
//                     child: ListView(
//                       controller: scrollController,
//                       padding: const EdgeInsets.all(15),
//                       children: [
//                         SizedBox(
//                             height: MediaQuery.sizeOf(context).height * .02),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.start,
//                           children: [
//                             Text(
//                               tr(LocaleKeys.Welcome_to),
//                               style: const TextStyle(
//                                 fontSize: 20,
//                                 fontWeight: FontWeight.bold,
//                               ),
//                             ),
//                             const SizedBox(width: 10),
//                             SvgIcon(
//                               icon: Assets.icons.renvo,
//                               color: StyleRepo.blue,
//                               size: 70,
//                             ),
//                           ],
//                         ),
//                         const SizedBox(width: 16),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.start,
//                           children: [
//                             Text(
//                               tr(LocaleKeys.Login_to),
//                               style: const TextStyle(
//                                 fontSize: 20,
//                                 fontWeight: FontWeight.bold,
//                               ),
//                             ),
//                             const SizedBox(width: 16),
//                             Assets.icons.smile.svg(),
//                           ],
//                         ),
//                         Text(
//                           tr(LocaleKeys.Enter_the_following),
//                           style: const TextStyle(
//                             fontSize: 11,
//                             fontWeight: FontWeight.bold,
//                           ),
//                         ),
//                         const SizedBox(height: 25),
//                         Text(
//                           tr(LocaleKeys.Phone_Number),
//                           style: const TextStyle(
//                             fontSize: 14,
//                             fontWeight: FontWeight.bold,
//                           ),
//                         ),
//                         TextFormField(
//                           keyboardType: TextInputType.phone,
//                           //     controller: controller.phoneNumber,
//                           decoration: InputDecoration(
//                             prefixIcon: Row(
//                               mainAxisSize: MainAxisSize.min,
//                               children: [
//                                 const SizedBox(width: 8),
//                                 Assets.icons.call.svg(),
//                                 const SizedBox(width: 8),
//                                 Assets.icons.line.svg(),
//                                 const SizedBox(width: 8),
//                               ],
//                             ),
//                             hintText: tr(LocaleKeys.Ex),
//                           ),
//                           validator: (value) {
//                             if (value!.isEmpty) {
//                               return tr(LocaleKeys.This_field_is_required);
//                             }
//                             if (!value.contains("+963")) {
//                               return tr(LocaleKeys.Wrong_phone);
//                             }
//                             return null;
//                           },
//                         ),
//                         const SizedBox(height: 30),
//                         Text(
//                           tr(LocaleKeys.Password),
//                           style: const TextStyle(
//                             fontSize: 14,
//                             fontWeight: FontWeight.bold,
//                           ),
//                         ),
//                         TextFormField(
//                           //   controller: controller.password,
//                           decoration: InputDecoration(
//                             prefixIcon: Row(
//                               mainAxisSize: MainAxisSize.min,
//                               children: [
//                                 const SizedBox(width: 8),
//                                 Assets.icons.pass.svg(),
//                                 const SizedBox(width: 8),
//                                 Assets.icons.line.svg(),
//                                 const SizedBox(width: 8),
//                               ],
//                             ),
//                             hintText: '*** *** ***',
//                           ),
//                           validator: (value) {
//                             if (value!.isEmpty) {
//                               return tr(LocaleKeys.This_field_is_required);
//                             }
//                             // if (!controller.passwordRegex.hasMatch(value)) {
//                             //   return tr(LocaleKeys.Password_must_be);
//                             // }
//                             return null;
//                           },
//                         ),
//                         Align(
//                           alignment: Alignment.centerRight,
//                           child: Text(
//                             tr(LocaleKeys.Forget_Passwords),
//                             style: const TextStyle(
//                               fontSize: 14,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                         ),
//                         const SizedBox(height: 100),
//                         Center(
//                           child: ElevatedButton(
//                             onPressed: () {},
//                             child: Text(
//                               tr(LocaleKeys.Login),
//                               style: const TextStyle(fontSize: 16),
//                             ),
//                           ),
//                         ),
//                         const SizedBox(height: 16),
//                         TextButton(
//                           onPressed: () => Get.toNamed(Pages.register.value),
//                           child: Text(
//                             tr(LocaleKeys.Sign_up),
//                             style: const TextStyle(
//                               fontSize: 16,
//                               color: StyleRepo.green,
//                             ),
//                           ),
//                         ),
//                         const SizedBox(height: 20),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             Assets.icons.guest.svg(),
//                             const SizedBox(width: 10),
//                             IconButton(
//                               onPressed: () {},
//                               icon: Text(
//                                 tr(LocaleKeys.Join_guest),
//                                 style: const TextStyle(
//                                   fontSize: 16,
//                                   color: StyleRepo.blue,
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                         const SizedBox(height: 16),
//                       ],
//                     ),
//                   ),
//                 );
//               },
//             ),
//             // 🧾 إضافة DraggableScrollableSheet هنا كما في كودك السابق
//           ],
//         ),
//       ),
//     );
//   }
// }
