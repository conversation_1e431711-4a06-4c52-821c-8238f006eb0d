import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/features/addOrder/widgets/AddOrderCard.dart';
import 'package:renvo/features/addOrder/widgets/CardOrder.dart';
import 'package:renvo/gen/assets.gen.dart';

class AddOrderPage extends StatelessWidget {
  const AddOrderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AddOrderCard(
      CardOrder: Positioned(
        top: MediaQuery.of(context).size.height * .1,
        right: 0,
        left: 0,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            children: [
              Assets.icons.rectangle.svg(width: 137, height: 137),
              Text(
                tr(LocaleKeys.Add_Order),
                style: Theme.of(context)
                    .textTheme
                    .headlineSmall!
                    .copyWith(color: StyleRepo.white),
              ),
              Text(
                tr(LocaleKeys.Select_Services),
                maxLines: 1,
                style: Theme.of(context)
                    .textTheme
                    .labelLarge!
                    .copyWith(color: StyleRepo.white),
              ),
              SizedBox(
                height: 31,
              ),
              CardOrder(
                title: tr(LocaleKeys.Household_Services),
                subtitle: tr(LocaleKeys.Cleaning_Ironing),
                picture: Assets.icons.houseServices.svg(),
              ),
              SizedBox(
                height: 10,
              ),
              CardOrder(
                title: tr(LocaleKeys.Professional_Services),
                subtitle: tr(LocaleKeys.Electrical_Plumbing),
                picture: Assets.icons.professionalServices.svg(),
              ),
              SizedBox(
                height: 10,
              ),
              CardOrder(
                title: tr(LocaleKeys.Personal_Services),
                subtitle: tr(LocaleKeys.Personal_Training),
                picture: Assets.icons.personalServices.svg(),
              ),
              SizedBox(
                height: 10,
              ),
              CardOrder(
                title: tr(LocaleKeys.Logistical_Services),
                subtitle: tr(LocaleKeys.Transport_Deliveries),
                picture: Assets.icons.logisticalServices.svg(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
