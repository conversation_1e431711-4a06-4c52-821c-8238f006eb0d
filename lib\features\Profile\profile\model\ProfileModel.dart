import 'dart:convert';

class ProfileModel {
  int id;
  String firstName;
  String lastName;
  String? email;
  String nationalId;
  String dialCountryCode;
  String phone;
  Gender gender;
  IDcard avatar;
  IDcard? iDcard;
  int? customerId;
  int? rate;
  int? ordersCnt;
  int? points;
  int? pointsInSp;

  ProfileModel({
    required this.id,
    required this.firstName,
    required this.lastName,
    this.email,
    required this.nationalId,
    required this.dialCountryCode,
    required this.phone,
    required this.gender,
    required this.avatar,
    this.iDcard,
    this.customerId,
    this.rate,
    this.ordersCnt,
    this.points,
    this.pointsInSp,
  });

  factory ProfileModel.fromRawJson(String str) =>
      ProfileModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProfileModel.fromJson(Map<String, dynamic> json) => ProfileModel(
        id: json["id"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        email: json["email"],
        nationalId: json["nationalID"],
        dialCountryCode: json["dial_country_code"],
        phone: json["phone"],
        gender: Gender.fromJson(json["gender"]),
        avatar: IDcard.fromJson(json["avatar"]),
        iDcard: IDcard.fromJson(json["IDcard"]),
        customerId: json["customer_id"],
        rate: json["rate"],
        ordersCnt: json["orders_cnt"],
        points: json["points"],
        pointsInSp: json["points_in_sp"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "first_name": firstName,
        "last_name": lastName,
        "email": email,
        "nationalID": nationalId,
        "dial_country_code": dialCountryCode,
        "phone": phone,
        "gender": gender.toJson(),
        "avatar": avatar.toJson(),
        "IDcard": iDcard!.toJson(),
        "customer_id": customerId,
        "rate": rate,
        "orders_cnt": ordersCnt,
        "points": points,
        "points_in_sp": pointsInSp,
      };
}

class IDcard {
  String originalUrl;

  IDcard({
    required this.originalUrl,
  });

  factory IDcard.fromRawJson(String str) => IDcard.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory IDcard.fromJson(Map<String, dynamic> json) => IDcard(
        originalUrl: json["original_url"],
      );

  Map<String, dynamic> toJson() => {
        "original_url": originalUrl,
      };
}

class Gender {
  int id;
  String name;

  Gender({
    required this.id,
    required this.name,
  });

  factory Gender.fromRawJson(String str) => Gender.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Gender.fromJson(Map<String, dynamic> json) => Gender(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}
