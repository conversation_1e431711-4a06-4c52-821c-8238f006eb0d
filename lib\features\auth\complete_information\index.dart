import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/core/widgets/auth_card.dart';
import 'package:renvo/core/widgets/image.dart';
import 'package:renvo/features/auth/complete_information/controller.dart';
import 'package:renvo/gen/assets.gen.dart';
import '../../../core/localization/strings.dart';

class CompleteInformationPage extends StatelessWidget {
  const CompleteInformationPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CompleteInformationPageController());

    return AuthCard(
      leading: true,
      card: Padding(
        padding: const EdgeInsets.all(20),
        child: SingleChildScrollView(
          child: Form(
            key: controller.formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(tr(LocaleKeys.Complete_information),
                    style: Theme.of(context).textTheme.headlineSmall),
                const SizedBox(height: 6),
                Text(tr(LocaleKeys.add_your_information),
                    style: Theme.of(context).textTheme.bodyMedium),
                const SizedBox(height: 30),

                // صورة الملف الشخصي

                FormField<String>(
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return tr(LocaleKeys.The_image_is_required);
                    }
                    return null;
                  },
                  builder: (state) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Obx(() => controller.profileImage.isEmpty
                                ? Container(
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: state.hasError
                                            ? Theme.of(context)
                                                .colorScheme
                                                .error
                                            : StyleRepo.lightGrey,
                                      ),
                                    ),
                                    child: Assets.icons.person.svg(),
                                  )
                                : AppImage(
                                    path: controller.profileImage.value,
                                    type: ImageType.File,
                                    height: 80,
                                    width: 80,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(),
                                    ),
                                  )),
                            SizedBox(width: 12),
                            InkWell(
                              onTap: () async {
                                await controller.pickImage('profile');
                                state.didChange(controller.profileImage.value);
                              },
                              child: Text(tr(LocaleKeys.Add_Profile_Photo),
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium!
                                      .copyWith(
                                        fontWeight: FontWeight.bold,
                                        decoration: TextDecoration.underline,
                                      )),
                            ),
                          ],
                        ),
                        if (state.hasError) SizedBox(height: 8),
                        if (state.hasError)
                          Text(state.errorText!,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(
                                    color: Theme.of(context).colorScheme.error,
                                  )),
                      ],
                    );
                  },
                ),

                const SizedBox(height: 22),
                Text(tr(LocaleKeys.ID_photo),
                    style: Theme.of(context).textTheme.bodyMedium),
                const SizedBox(height: 5),
                FormField<String>(
                  initialValue: controller.idImage.value,
                  builder: (state) {
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Obx(() => controller.idImage.isEmpty
                            ? Container(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: state.hasError
                                        ? Theme.of(context).colorScheme.error
                                        : StyleRepo.lightGrey,
                                  ),
                                ),
                                child: Assets.icons.camera.svg(),
                              )
                            : AppImage(
                                path: controller.idImage.value,
                                type: ImageType.File,
                                height: 80,
                                width: 80,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(),
                                ),
                              )),
                        SizedBox(width: 12),
                        InkWell(
                          onTap: () async {
                            await controller.pickImage('id');
                            state.didChange(controller.idImage.value);
                          },
                          child: Text(tr(LocaleKeys.Add_Photo),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge!
                                  .copyWith(
                                    color: StyleRepo.lightGrey,
                                  )),
                        ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 22),
                Text(tr(LocaleKeys.First_Name),
                    style: Theme.of(context).textTheme.bodyMedium),
                const SizedBox(height: 6),
                TextFormField(
                  controller: controller.First_name,
                  decoration: InputDecoration(
                    prefixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(width: 8),
                        Assets.icons.circle.svg(),
                        const SizedBox(width: 8),
                        Assets.icons.line.svg(),
                        const SizedBox(width: 8),
                      ],
                    ),
                    hintText: tr(LocaleKeys.User_name),
                  ),
                  validator: (value) {
                    if (value!.isEmpty) {
                      return tr(LocaleKeys.This_field_is_required);
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 22),
                Text(tr(LocaleKeys.Last_Name),
                    style: Theme.of(context).textTheme.bodyMedium),
                const SizedBox(height: 6),
                TextFormField(
                  controller: controller.Last_name,
                  decoration: InputDecoration(
                    prefixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(width: 8),
                        Assets.icons.circle.svg(),
                        const SizedBox(width: 8),
                        Assets.icons.line.svg(),
                        const SizedBox(width: 8),
                      ],
                    ),
                    hintText: tr(LocaleKeys.Last_Name),
                  ),
                  validator: (value) {
                    if (value!.isEmpty) {
                      return tr(LocaleKeys.This_field_is_required);
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 15),
                Text(tr(LocaleKeys.Email),
                    style: Theme.of(context).textTheme.bodyMedium),
                const SizedBox(height: 6),
                TextFormField(
                  controller: controller.email,
                  decoration: InputDecoration(
                    prefixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(width: 8),
                        Assets.icons.message.svg(),
                        const SizedBox(width: 8),
                        Assets.icons.line.svg(),
                        const SizedBox(width: 8),
                      ],
                    ),
                    hintText: tr(LocaleKeys.user_name_gmail),
                  ),
                  validator: (value) {
                    // if (value!.isEmpty) {
                    //   return tr(LocaleKeys.This_field_is_required);
                    // }
                    // if (!value.contains("@")) {
                    //   return tr(LocaleKeys.Wrong_email);
                    // }
                    return null;
                  },
                ),
                const SizedBox(height: 15),
                Text(tr(LocaleKeys.National_number),
                    style: Theme.of(context).textTheme.bodyMedium),
                const SizedBox(height: 6),
                TextFormField(
                  controller: controller.National_number,
                  decoration: InputDecoration(
                    prefixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(width: 8),
                        Assets.icons.national.svg(),
                        const SizedBox(width: 8),
                        Assets.icons.line.svg(),
                        const SizedBox(width: 8),
                      ],
                    ),
                    hintText: tr(LocaleKeys.add_national_number),
                  ),
                ),
                const SizedBox(height: 15),
                Text(tr(LocaleKeys.Gender),
                    style: Theme.of(context).textTheme.bodyMedium),
                const SizedBox(height: 6),
                DropdownButtonFormField<String>(
                  style: Theme.of(context).textTheme.labelMedium,
                  value: controller.gender.text.isNotEmpty
                      ? controller.gender.text
                      : null,
                  onChanged: (value) {
                    controller.gender.text = value!;
                  },
                  items: [
                    DropdownMenuItem(
                      value: '1',
                      child: Text(tr(LocaleKeys.Male)),
                    ),
                    DropdownMenuItem(
                      value: '2',
                      child: Text(tr(LocaleKeys.Female)),
                    ),
                  ],
                  decoration: InputDecoration(
                    prefixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(width: 8),
                        Assets.icons.gender.svg(),
                        const SizedBox(width: 8),
                        Assets.icons.line.svg(),
                        const SizedBox(width: 8),
                      ],
                    ),
                    hintText: tr(LocaleKeys.Male),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return tr(LocaleKeys.Please_select_gender);
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 30),

                Center(
                  child: ElevatedButton(
                    onPressed: () {
                      controller.confirm();
                    },
                    child: Text(
                      tr(LocaleKeys.Confirm),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
