import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/routes/routes.dart';
import 'package:renvo/core/services/state_management/widgets/obs_widget.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/features/addOrder/widgets/CardOrder.dart';
import 'package:renvo/features/home/<USER>';
import 'package:renvo/features/join%20provider/controller.dart';
import 'package:renvo/features/join%20provider/widgets/servicesCard.dart';
import 'package:renvo/gen/assets.gen.dart';

class JoinProviderPage extends StatelessWidget {
  const JoinProviderPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(JoinProviderPageController());
    final controllerCat = Get.find<HomePageController>();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: StyleRepo.blue,
        leading: IconButton(
          onPressed: () => Get.back(),
          icon: Assets.icons.back.svg(color: StyleRepo.white),
          iconSize: 24,
        ),
      ),
      body: Container(
        padding: EdgeInsets.all(30),
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Color(0x40000000),
              blurRadius: 4,
              offset: Offset(0, 4),
            ),
          ],
          gradient: LinearGradient(
            colors: [
              StyleRepo.blue,
              StyleRepo.whiteblue,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                tr(LocaleKeys.SelectServicesType),
                style: Theme.of(context)
                    .textTheme
                    .headlineSmall!
                    .copyWith(color: StyleRepo.white),
              ),
              SizedBox(
                height: 21,
              ),
              Center(
                child: Text(
                  tr(LocaleKeys.WelcomeToRenva),
                  style: Theme.of(context)
                      .textTheme
                      .labelSmall!
                      .copyWith(color: StyleRepo.white),
                ),
              ),
              SizedBox(
                height: 21,
              ),
              ObsListBuilder(
                  obs: controllerCat.Category,
                  builder: (context, Category) {
                    return Column(
                      children: List.generate(
                        Category.length,
                        (index) => ServicesCard(
                          title: controllerCat.Category[index].title,
                          subtitle:
                              controllerCat.Category[index].description ?? '',
                          picture: Assets.icons.houseServices.svg(),
                          isChecked: controller.checkboxes[index],
                        ),
                      ),
                    );
                  }),
              SizedBox(
                height: 65,
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        height: 100,
        color: StyleRepo.whiteblue,
        padding: EdgeInsets.symmetric(horizontal: 50, vertical: 20),
        child: ElevatedButton(
          onPressed: () => Get.toNamed(Pages.ProviderInfo.value),
          child: Text(tr(LocaleKeys.Next),
              style: Theme.of(context)
                  .textTheme
                  .labelLarge!
                  .copyWith(color: StyleRepo.blue)),
          style: ButtonStyle(
            backgroundColor: WidgetStatePropertyAll(StyleRepo.white),
          ),
        ),
      ),
    );
  }
}
