import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/features/Chat/index.dart';
import 'package:renvo/features/MyOrder/index.dart';
import 'package:renvo/features/Profile/profile/index.dart';
import 'package:renvo/features/addOrder/index.dart';
import 'package:renvo/features/home/<USER>';

import 'package:renvo/features/main/controller.dart';
import 'package:renvo/features/main/widgets/nav_bar.dart';

class MainPage extends StatelessWidget {
  const MainPage({super.key});
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(MainPageController());

    return Scaffold(
      body: Stack(
        children: [
          MediaQuery.removePadding(
            context: context,
            removeTop: true,
            child: Obx(
              () {
                switch (controller.currentPage.value) {
                  case 0:
                    return HomePage();
                  case 1:
                    return MyOrderPage();
                  case 3:
                    return ChatPage();
                  case 4:
                    return ProfilePage();
                  default:
                    return ColoredBox(color: StyleRepo.green);
                }
              },
            ),
          ),
          Padding(
            padding:
                EdgeInsets.only(top: MediaQuery.of(context).size.height * .9),
            child: FloatingNavBar(),
          ),
        ],
      ),
    );
  }
}
