import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:renvo/core/demo/media.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/core/widgets/image.dart';

import 'package:renvo/gen/assets.gen.dart';

class ChatPage extends StatelessWidget {
  ChatPage({super.key});
  @override
  Widget build(BuildContext context) {
    // final controller = Get.put(ChatPageController());

    return Stack(
      fit: StackFit.expand,
      children: [
        SizedBox(
          height: MediaQuery.of(context).size.height * .2,
          width: double.infinity,
          child: ColoredBox(color: StyleRepo.bluemedium),
        ),
        Positioned(
          top: 0,
          child: Center(child: Assets.icons.logoHome.svg()),
        ),
        Positioned(
          right: -10,
          top: -10,
          child: Center(child: Assets.icons.logohome2.svg()),
        ),
        <PERSON><PERSON>(
          alignment: context.locale.languageCode == 'ar'
              ? Alignment.topRight
              : Alignment.topLeft,
          child: Padding(
            padding: const EdgeInsets.only(top: 90, left: 20, right: 20),
            child: Text(
              tr(LocaleKeys.Messages),
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                    color: StyleRepo.white,
                  ),
              textAlign: TextAlign.start,
            ),
          ),
        ),

        // Positioned(
        //   left: 20,
        //   top: 90,
        //   child: Text(
        //     tr(LocaleKeys.Messages),
        //     style: Theme.of(context).textTheme.titleLarge!.copyWith(
        //           color: StyleRepo.white, // أو StyleRepo.blue مثلاً
        //         ),
        //   ),
        // ),
        Positioned(
          top: MediaQuery.of(context).size.height * .2,
          left: 0,
          right: 0,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 20),
            height: MediaQuery.of(context).size.height * .9,
            decoration: BoxDecoration(
              color: StyleRepo.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(36),
                topRight: Radius.circular(36),
              ),
            ),
            child: ListView.separated(
              itemCount: 10,
              separatorBuilder: (_, __) => const Divider(
                color: StyleRepo.lightGrey2,
                thickness: 1,
              ),
              itemBuilder: (context, index) {
                return ListTile(
                  leading: AppImage(
                    path: DemoMedia.getAppRandomImage,
                    type: ImageType.CachedNetwork,
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                  ),
                  title: Text(
                    "Joni providessr ",
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  subtitle: Text(
                    "Ex elit in ipsum exercitation ex voluptate nulla minim cillum velit qui dolore culpa.",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  trailing: Text("7 minute"),
                );
              },
            ),
          ),
        )
      ],
    );
  }
}
