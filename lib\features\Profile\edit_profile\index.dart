import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/core/widgets/image.dart';
import 'package:renvo/features/Profile/edit_profile/controller.dart';

import 'package:renvo/gen/assets.gen.dart';

class EditProfilePage extends StatelessWidget {
  const EditProfilePage({super.key});
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(EditProfilePageController());

    return Scaffold(
      body: Stack(
        children: [
          Positioned(
            top: 0,
            right: 0,
            child: IgnorePointer(
              child: Opacity(
                opacity: 0.04,
                child: Assets.icons.logoHome.svg(width: 400, height: 400),
              ),
            ),
          ),
          Positioned(
            top: 50,
            left: 10,
            child: IconButton(
              onPressed: () => Get.back(),
              icon: Assets.icons.back.svg(color: StyleRepo.black),
              iconSize: 24,
            ),
          ),
          Positioned(
            top: 55,
            left: 60,
            child: Text(tr(LocaleKeys.Edit_profile),
                style: Theme.of(context).textTheme.titleMedium),
          ),
          Positioned(
            top: 80,
            bottom: 0,
            left: 0,
            right: 0,
            child: Form(
              key: controller.formKey,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: ProfileForm(
                  controller: controller,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ProfileForm extends StatelessWidget {
  const ProfileForm({
    super.key,
    required this.controller,
  });

  final EditProfilePageController controller;

  @override
  Widget build(BuildContext context) {
    return ListView(
      children: [
        Center(
            child: FormField<String>(
          initialValue: controller.image.value,
          builder: (state) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Stack(
                  //  alignment: Alignment.center,
                  children: [
                    controller.image.value.isNotEmpty
                        ? AppImage(
                            height: 140,
                            width: 140,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                            ),
                            path: controller.image.value,
                            type: ImageType.File,
                            fit: BoxFit.cover,
                          )
                        : AppImage(
                            path: controller.image.value,
                            type: ImageType.CachedNetwork,
                            height: 140,
                            width: 140,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                            ),
                          ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: TextButton(
                        onPressed: () async {
                          await controller.pickImage();
                          state.didChange(controller.image.value);
                          controller.image.refresh();
                        },
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                          shape: const CircleBorder(),
                        ),
                        child: Container(
                          width: 41,
                          height: 41,
                          decoration: const BoxDecoration(
                            color: StyleRepo.white,
                            shape: BoxShape.circle,
                          ),
                          child: Assets.icons.camera.svg(),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        )),
        SizedBox(
          height: 12,
        ),
        Text(tr(LocaleKeys.First_Name),
            style: Theme.of(context).textTheme.bodyMedium),
        const SizedBox(height: 6),
        TextFormField(
          controller: controller.First_name,
          decoration: InputDecoration(
            prefixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(width: 8),
                Assets.icons.circle.svg(),
                const SizedBox(width: 8),
                Assets.icons.line.svg(),
                const SizedBox(width: 8),
              ],
            ),
          ),
          validator: (value) {
            if (value!.isEmpty) {
              return tr(LocaleKeys.This_field_is_required);
            }
            return null;
          },
        ),
        const SizedBox(height: 15),
        Text(tr(LocaleKeys.Last_Name),
            style: Theme.of(context).textTheme.bodyMedium),
        const SizedBox(height: 6),
        TextFormField(
          controller: controller.Last_name,
          decoration: InputDecoration(
            prefixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(width: 8),
                Assets.icons.circle.svg(),
                const SizedBox(width: 8),
                Assets.icons.line.svg(),
                const SizedBox(width: 8),
              ],
            ),
          ),
          validator: (value) {
            if (value!.isEmpty) {
              return tr(LocaleKeys.This_field_is_required);
            }
            return null;
          },
        ),
        const SizedBox(height: 15),
        Text(tr(LocaleKeys.Email),
            style: Theme.of(context).textTheme.bodyMedium),
        const SizedBox(height: 6),
        TextFormField(
          controller: controller.email,
          decoration: InputDecoration(
            prefixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(width: 8),
                Assets.icons.message.svg(),
                const SizedBox(width: 8),
                Assets.icons.line.svg(),
                const SizedBox(width: 8),
              ],
            ),
          ),
          validator: (value) {
            // if (value!.isEmpty) {
            //   return tr(LocaleKeys.This_field_is_required);
            // }
            // if (!value.contains("@gmail.com")) {
            //   return tr(LocaleKeys.Wrong_email);
            // }
            return null;
          },
        ),
        const SizedBox(height: 15),
        Text(tr(LocaleKeys.Phone_Number),
            style: Theme.of(context).textTheme.bodyMedium),
        const SizedBox(height: 6),
        TextFormField(
          controller: controller.phoneNumber,
          decoration: InputDecoration(
            prefixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(width: 8),
                Assets.icons.national.svg(),
                const SizedBox(width: 8),
                Assets.icons.line.svg(),
                const SizedBox(width: 8),
              ],
            ),
          ),
          validator: (value) {
            if (value!.isEmpty) {
              return tr(LocaleKeys.This_field_is_required);
            }
            if (!RegExp(r'^[0-9]+$').hasMatch(value))
              return tr(LocaleKeys.only_numbers_allowed);

            return null;
          },
        ),
        const SizedBox(height: 15),
        Text(tr(LocaleKeys.National_number),
            style: Theme.of(context).textTheme.bodyMedium),
        const SizedBox(height: 6),
        TextFormField(
          controller: controller.National_number,
          decoration: InputDecoration(
            prefixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(width: 8),
                Assets.icons.national.svg(),
                const SizedBox(width: 8),
                Assets.icons.line.svg(),
                const SizedBox(width: 8),
              ],
            ),
          ),
          validator: (value) {
            if (value!.isEmpty) {
              return tr(LocaleKeys.This_field_is_required);
            }
            return null;
          },
        ),
        SizedBox(
          height: 15,
        ),
        Text(tr(LocaleKeys.Gender),
            style: Theme.of(context).textTheme.bodyMedium),
        DropdownButtonFormField<String>(
          style: Theme.of(context).textTheme.labelMedium,
          value:
              controller.gender.text.isNotEmpty ? controller.gender.text : null,
          onChanged: (value) {
            controller.gender.text = value!;
          },
          items: [
            DropdownMenuItem(
              value: '1',
              child: Text(tr(LocaleKeys.Male)),
            ),
            DropdownMenuItem(
              value: '2',
              child: Text(tr(LocaleKeys.Female)),
            ),
          ],
          decoration: InputDecoration(
            prefixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(width: 8),
                Assets.icons.gender.svg(),
                const SizedBox(width: 8),
                Assets.icons.line.svg(),
                const SizedBox(width: 8),
              ],
            ),
            hintText: tr(LocaleKeys.Male),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return tr(LocaleKeys.Please_select_gender);
            }
            return null;
          },
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          child: ElevatedButton(
            onPressed: controller.confirm,
            child: Text(
              tr(LocaleKeys.save),
              style: TextStyle(color: StyleRepo.white, fontSize: 16),
            ),
          ),
        ),
      ],
    );
  }
}
