import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/widgets/auth_card.dart';
import 'package:renvo/features/auth/Resend%20Password/controller.dart';
import 'package:renvo/gen/assets.gen.dart';

class ResendPassPage extends StatelessWidget {
  const ResendPassPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ResendPassPageController());
    return AuthCard(
      leading: false,
      card: Form(
        key: controller.formKey,
        child: ListView(
          padding: EdgeInsets.all(15),
          children: [
            SizedBox(height: MediaQuery.sizeOf(context).height * .01),
            Text(tr(LocaleKeys.ResendPass),
                style: Theme.of(context).textTheme.headlineSmall),
            Sized<PERSON>ox(
              height: 25,
            ),
            Text(tr(LocaleKeys.Password),
                style: Theme.of(context).textTheme.bodyMedium),
            const SizedBox(height: 6),
            TextFormField(
              controller: controller.password,
              decoration: InputDecoration(
                prefixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(width: 8),
                    Assets.icons.pass.svg(),
                    SizedBox(width: 8),
                    Assets.icons.line.svg(),
                    SizedBox(width: 8),
                  ],
                ),
                hintText: '*** *** ***',
              ),
              validator: (value) {
                if (value!.isEmpty) {
                  return tr(LocaleKeys.This_field_is_required);
                }
                if (!controller.passwordRegex.hasMatch(value)) {
                  return tr(LocaleKeys.Password_must_be);
                }

                return null;
              },
            ),
            SizedBox(height: 30),
            Text(tr(LocaleKeys.Confirm_Password),
                style: Theme.of(context).textTheme.bodyMedium),
            const SizedBox(height: 6),
            TextFormField(
              controller: controller.confirmpassword,
              decoration: InputDecoration(
                prefixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(width: 8),
                    Assets.icons.pass.svg(),
                    SizedBox(width: 8),
                    Assets.icons.line.svg(),
                    SizedBox(width: 8),
                  ],
                ),
                hintText: '*** *** ***',
              ),
              validator: (value) {
                if (controller.password.text != value || value!.isEmpty) {
                  return tr(LocaleKeys.Passwords_do_not_match);
                }

                return null;
              },
            ),
            SizedBox(height: 80),
            Center(
              child: ElevatedButton(
                onPressed: controller.confirm,
                child: Text(
                  tr(LocaleKeys.Confirm_Password),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
