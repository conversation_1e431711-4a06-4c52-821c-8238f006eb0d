// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/features/Profile/edit_profile/index.dart';
import 'package:renvo/features/addOrder/index.dart';
import 'package:renvo/features/auth/Resend%20Password/index.dart';
import 'package:renvo/features/auth/Verify/index.dart';
import 'package:renvo/features/auth/complete_information/index.dart';
import 'package:renvo/features/auth/login/index.dart';
import 'package:renvo/features/auth/register/index.dart';
import 'package:renvo/features/Stories/index.dart';
import 'package:renvo/features/join%20provider/index.dart';
import 'package:renvo/features/join%20provider/widgets/providerInfo.dart';
import 'package:renvo/features/main/index.dart';
import 'package:renvo/features/splash/index.dart';

class AppRouting {
  static GetPage unknownRoute =
      GetPage(name: "/unknown", page: () => SizedBox());

  static GetPage initialRoute = GetPage(
    name: "/",
    page: () => SplashScreen(),
  );

  static List<GetPage> routes = [
    initialRoute,
    ...Pages.values.map((e) => e.page),
  ];
}

enum Pages {
  //Auth
  login,
  register,

  //
  complete,
  home,
  verify,
  Stories,
  ResendPass,
  AddOrder,
  JoinProvider,
  ProviderInfo,
//
  Edit_profile;

  String get value => '/$name';

  GetPage get page => switch (this) {
        home => GetPage(
            name: value,
            page: () => MainPage(),
          ),
        login => GetPage(
            name: value,
            page: () => LoginPage(),
          ),
        register => GetPage(
            name: value,
            page: () => RegisterPage(),
          ),
        complete => GetPage(
            name: value,
            page: () => CompleteInformationPage(),
          ),
        verify => GetPage(
            name: value,
            page: () => VerifyPage(),
          ),
        Edit_profile => GetPage(
            name: value,
            page: () => EditProfilePage(),
          ),
        Stories => GetPage(
            name: value,
            page: () => StoriesPage(),
          ),
        AddOrder => GetPage(
            name: value,
            page: () => AddOrderPage(),
          ),
        ResendPass => GetPage(
            name: value,
            page: () => ResendPassPage(),
          ),
        JoinProvider => GetPage(
            name: value,
            page: () => JoinProviderPage(),
          ),
        ProviderInfo => GetPage(
            name: value,
            page: () => ProviderInfoPage(),
          ),
      };
}
