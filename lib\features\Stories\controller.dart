import 'dart:async';

import 'package:carousel_slider/carousel_controller.dart';
import 'package:get/get.dart';
import 'package:renvo/core/demo/media.dart';

class StoriesPageController extends GetxController {
  final RxInt currentAd = 0.obs;
  final List ads = [
    DemoMedia.getAppRandomImage,
    DemoMedia.getAppRandomImage,
    DemoMedia.getAppRandomImage,
  ];
  final RxDouble progress = 0.0.obs;
  Timer? _timer;
  final CarouselSliderController carouselController =
      CarouselSliderController();

  @override
  void onInit() {
    super.onInit();
    startProgressTimer();
  }

  void startProgressTimer() {
    _timer?.cancel();
    progress.value = 0.0;

    _timer = Timer.periodic(Duration(milliseconds: 50), (timer) {
      progress.value += 0.01;
      if (progress.value >= 1.0) {
        timer.cancel();
        goToNextStory();
      }
    });
  }

  void goToNextStory() {
    if (currentAd.value < ads.length - 1) {
      currentAd.value++;
      carouselController.animateToPage(currentAd.value);

      startProgressTimer();
    } else {
      Get.back();
    }
  }

  void onPageChanged(int index) {
    currentAd.value = index;
    startProgressTimer();
  }

  @override
  void onClose() {
    _timer?.cancel();
    super.onClose();
  }
}
