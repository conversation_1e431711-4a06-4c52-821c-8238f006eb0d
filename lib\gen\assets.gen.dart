/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/3points.svg
  SvgGenImage get a3points => const SvgGenImage('assets/icons/3points.svg');

  /// File path: assets/icons/Addstory.svg
  SvgGenImage get addstory => const SvgGenImage('assets/icons/Addstory.svg');

  /// File path: assets/icons/ArrowRight.svg
  SvgGenImage get arrowRight =>
      const SvgGenImage('assets/icons/ArrowRight.svg');

  /// File path: assets/icons/DeleteOrder.svg
  SvgGenImage get deleteOrder =>
      const SvgGenImage('assets/icons/DeleteOrder.svg');

  /// File path: assets/icons/Gender.svg
  SvgGenImage get gender => const SvgGenImage('assets/icons/Gender.svg');

  /// File path: assets/icons/HouseServices.svg
  SvgGenImage get houseServices =>
      const SvgGenImage('assets/icons/HouseServices.svg');

  /// File path: assets/icons/JoinUs.svg
  SvgGenImage get joinUs => const SvgGenImage('assets/icons/JoinUs.svg');

  /// File path: assets/icons/JoinUs22.png
  AssetGenImage get joinUs22 =>
      const AssetGenImage('assets/icons/JoinUs22.png');

  /// File path: assets/icons/LogisticalServices.svg
  SvgGenImage get logisticalServices =>
      const SvgGenImage('assets/icons/LogisticalServices.svg');

  /// File path: assets/icons/LoyaltyPoints .svg
  SvgGenImage get loyaltyPoints =>
      const SvgGenImage('assets/icons/LoyaltyPoints .svg');

  /// File path: assets/icons/MyOrder.svg
  SvgGenImage get myOrder => const SvgGenImage('assets/icons/MyOrder.svg');

  /// File path: assets/icons/PersonalServices.svg
  SvgGenImage get personalServices =>
      const SvgGenImage('assets/icons/PersonalServices.svg');

  /// File path: assets/icons/ProfessionalServices.svg
  SvgGenImage get professionalServices =>
      const SvgGenImage('assets/icons/ProfessionalServices.svg');

  /// File path: assets/icons/Profile.svg
  SvgGenImage get profile => const SvgGenImage('assets/icons/Profile.svg');

  /// File path: assets/icons/Profilepng.png
  AssetGenImage get profilepng =>
      const AssetGenImage('assets/icons/Profilepng.png');

  /// File path: assets/icons/Renva.svg
  SvgGenImage get renva => const SvgGenImage('assets/icons/Renva.svg');

  /// File path: assets/icons/View.svg
  SvgGenImage get view => const SvgGenImage('assets/icons/View.svg');

  /// File path: assets/icons/add.svg
  SvgGenImage get add => const SvgGenImage('assets/icons/add.svg');

  /// File path: assets/icons/alarm.svg
  SvgGenImage get alarm => const SvgGenImage('assets/icons/alarm.svg');

  /// File path: assets/icons/auth_backgraound.svg
  SvgGenImage get authBackgraound =>
      const SvgGenImage('assets/icons/auth_backgraound.svg');

  /// File path: assets/icons/back.svg
  SvgGenImage get back => const SvgGenImage('assets/icons/back.svg');

  /// File path: assets/icons/backhome.gif
  AssetGenImage get backhome =>
      const AssetGenImage('assets/icons/backhome.gif');

  /// File path: assets/icons/call.svg
  SvgGenImage get call => const SvgGenImage('assets/icons/call.svg');

  /// File path: assets/icons/camera.svg
  SvgGenImage get camera => const SvgGenImage('assets/icons/camera.svg');

  /// File path: assets/icons/cancel.svg
  SvgGenImage get cancel => const SvgGenImage('assets/icons/cancel.svg');

  /// File path: assets/icons/change_Pass.svg
  SvgGenImage get changePass =>
      const SvgGenImage('assets/icons/change_Pass.svg');

  /// File path: assets/icons/chat.svg
  SvgGenImage get chat => const SvgGenImage('assets/icons/chat.svg');

  /// File path: assets/icons/circle.svg
  SvgGenImage get circle => const SvgGenImage('assets/icons/circle.svg');

  /// File path: assets/icons/contact.svg
  SvgGenImage get contact => const SvgGenImage('assets/icons/contact.svg');

  /// File path: assets/icons/delete.svg
  SvgGenImage get delete => const SvgGenImage('assets/icons/delete.svg');

  /// File path: assets/icons/eye.svg
  SvgGenImage get eye => const SvgGenImage('assets/icons/eye.svg');

  /// File path: assets/icons/faq.svg
  SvgGenImage get faq => const SvgGenImage('assets/icons/faq.svg');

  /// File path: assets/icons/guest.svg
  SvgGenImage get guest => const SvgGenImage('assets/icons/guest.svg');

  /// File path: assets/icons/icon.png
  AssetGenImage get icon => const AssetGenImage('assets/icons/icon.png');

  /// File path: assets/icons/iconorder.svg
  SvgGenImage get iconorder => const SvgGenImage('assets/icons/iconorder.svg');

  /// File path: assets/icons/joinnnn.svg
  SvgGenImage get joinnnn => const SvgGenImage('assets/icons/joinnnn.svg');

  /// File path: assets/icons/key.svg
  SvgGenImage get key => const SvgGenImage('assets/icons/key.svg');

  /// File path: assets/icons/line.svg
  SvgGenImage get line => const SvgGenImage('assets/icons/line.svg');

  /// File path: assets/icons/linewhite.svg
  SvgGenImage get linewhite => const SvgGenImage('assets/icons/linewhite.svg');

  /// File path: assets/icons/loaytlypoint.png
  AssetGenImage get loaytlypoint =>
      const AssetGenImage('assets/icons/loaytlypoint.png');

  /// File path: assets/icons/location.svg
  SvgGenImage get location => const SvgGenImage('assets/icons/location.svg');

  /// File path: assets/icons/logo.svg
  SvgGenImage get logo => const SvgGenImage('assets/icons/logo.svg');

  /// File path: assets/icons/logo_home.svg
  SvgGenImage get logoHome => const SvgGenImage('assets/icons/logo_home.svg');

  /// File path: assets/icons/logohome2.svg
  SvgGenImage get logohome2 => const SvgGenImage('assets/icons/logohome2.svg');

  /// File path: assets/icons/logout.svg
  SvgGenImage get logout => const SvgGenImage('assets/icons/logout.svg');

  /// File path: assets/icons/love.svg
  SvgGenImage get love => const SvgGenImage('assets/icons/love.svg');

  /// File path: assets/icons/man.svg
  SvgGenImage get man => const SvgGenImage('assets/icons/man.svg');

  /// File path: assets/icons/message.svg
  SvgGenImage get message => const SvgGenImage('assets/icons/message.svg');

  /// File path: assets/icons/national.svg
  SvgGenImage get national => const SvgGenImage('assets/icons/national.svg');

  /// File path: assets/icons/notification.svg
  SvgGenImage get notification =>
      const SvgGenImage('assets/icons/notification.svg');

  /// File path: assets/icons/pass.svg
  SvgGenImage get pass => const SvgGenImage('assets/icons/pass.svg');

  /// File path: assets/icons/payment.png
  AssetGenImage get payment => const AssetGenImage('assets/icons/payment.png');

  /// File path: assets/icons/person.svg
  SvgGenImage get person => const SvgGenImage('assets/icons/person.svg');

  /// File path: assets/icons/privacy.svg
  SvgGenImage get privacy => const SvgGenImage('assets/icons/privacy.svg');

  /// File path: assets/icons/rectangle.svg
  SvgGenImage get rectangle => const SvgGenImage('assets/icons/rectangle.svg');

  /// File path: assets/icons/renvo.svg
  SvgGenImage get renvo => const SvgGenImage('assets/icons/renvo.svg');

  /// File path: assets/icons/search.svg
  SvgGenImage get search => const SvgGenImage('assets/icons/search.svg');

  /// File path: assets/icons/servicecam.svg
  SvgGenImage get servicecam =>
      const SvgGenImage('assets/icons/servicecam.svg');

  /// File path: assets/icons/share.svg
  SvgGenImage get share => const SvgGenImage('assets/icons/share.svg');

  /// File path: assets/icons/smile.svg
  SvgGenImage get smile => const SvgGenImage('assets/icons/smile.svg');

  /// File path: assets/icons/uploadPhoto.svg
  SvgGenImage get uploadPhoto =>
      const SvgGenImage('assets/icons/uploadPhoto.svg');

  /// File path: assets/icons/veify.svg
  SvgGenImage get veify => const SvgGenImage('assets/icons/veify.svg');

  /// List of all assets
  List<dynamic> get values => [
    a3points,
    addstory,
    arrowRight,
    deleteOrder,
    gender,
    houseServices,
    joinUs,
    joinUs22,
    logisticalServices,
    loyaltyPoints,
    myOrder,
    personalServices,
    professionalServices,
    profile,
    profilepng,
    renva,
    view,
    add,
    alarm,
    authBackgraound,
    back,
    backhome,
    call,
    camera,
    cancel,
    changePass,
    chat,
    circle,
    contact,
    delete,
    eye,
    faq,
    guest,
    icon,
    iconorder,
    joinnnn,
    key,
    line,
    linewhite,
    loaytlypoint,
    location,
    logo,
    logoHome,
    logohome2,
    logout,
    love,
    man,
    message,
    national,
    notification,
    pass,
    payment,
    person,
    privacy,
    rectangle,
    renvo,
    search,
    servicecam,
    share,
    smile,
    uploadPhoto,
    veify,
  ];
}

class $AssetsTranslationsGen {
  const $AssetsTranslationsGen();

  /// File path: assets/translations/ar.json
  String get ar => 'assets/translations/ar.json';

  /// File path: assets/translations/en.json
  String get en => 'assets/translations/en.json';

  /// List of all assets
  List<String> get values => [ar, en];
}

class Assets {
  const Assets._();

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsTranslationsGen translations = $AssetsTranslationsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
