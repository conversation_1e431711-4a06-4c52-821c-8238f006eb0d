import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/routes/routes.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/features/home/<USER>/StoryCard.dart';
import 'package:renvo/gen/assets.gen.dart';

class Homestories extends StatelessWidget {
  const Homestories({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: ClipRRect(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(24),
        ), // قص الحواف الخارجية ✅
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
          decoration: BoxDecoration(
            color: StyleRepo.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(tr(LocaleKeys.Curated_Stories),
                  style: Theme.of(context).textTheme.titleMedium),
              Text(tr(LocaleKeys.Discover_New_horizons),
                  style: Theme.of(context).textTheme.labelSmall!.copyWith(
                        color: StyleRepo.lightGrey, // أو StyleRepo.blue مثلاً
                      )),
              SizedBox(
                height: 16,
              ),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: List.generate(
                    8,
                    (index) => Padding(
                      padding: index == 7
                          ? EdgeInsets.zero
                          : const EdgeInsetsDirectional.only(end: 8),
                      child: InkWell(
                          onTap: () {
                            Get.toNamed(Pages.Stories.value);
                          },
                          child: StoryCard()),
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 16,
              ),
              Text(tr(LocaleKeys.Join_as_Services),
                  style: Theme.of(context).textTheme.titleMedium),
              // SizedBox(height: 6),
              Text(
                tr(LocaleKeys.Top_rated_service_providers),
                style: Theme.of(context).textTheme.labelSmall!.copyWith(
                      color: StyleRepo.lightGrey, // أو StyleRepo.blue مثلاً
                    ),
              ),
              SizedBox(height: 10),
              GestureDetector(
                  onTap: () => Get.toNamed(Pages.JoinProvider.value),
                  child: Assets.icons.joinUs.svg()),
              SizedBox(
                height: 100,
              )
            ],
          ),
          // ),

          // SizedBox(
          //   width: double.infinity, // أو حجم مخصص
          //   height: 150,
          //   child: ClipRRect(
          //     borderRadius: BorderRadius.circular(12),
          //     child: Assets.icons.imagehome.svg(
          //       fit: BoxFit.cover,
          //     ),
          //   ),
          // ),
        ),
      ),
    );
  }
}
