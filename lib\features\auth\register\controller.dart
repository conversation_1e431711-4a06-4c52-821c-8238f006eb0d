import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/routes/routes.dart';
import '../../../core/localization/localization.dart';
import '../../../core/services/rest_api/rest_api.dart';

class RegisterPageController extends GetxController {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController phoneNumber, password, Confirm_Password;
  var passwordRegex = RegExp(
    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$',
  );
  @override
  onInit() {
    phoneNumber = TextEditingController();
    password = TextEditingController();
    Confirm_Password = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    phoneNumber.dispose();
    password.dispose();
    Confirm_Password.dispose();

    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.register,
        method: RequestMethod.Post,
        body: {
          "phone": phoneNumber.text,
          "dial_country_code": "963",
          "password": password.text,
          "password_confirmation": Confirm_Password.text,
        },
      ),
    );

    if (response.success) {
      Get.toNamed(Pages.verify.value, arguments: {
        "phone": phoneNumber.text,
        "isforget": "no",
      } // تأكد أن المتغير معرف
          );
    } else {
      Get.snackbar(tr(LocaleKeys.Error_Number),
          tr(LocaleKeys.This_Number_already_exists));
    }
  }

  var isArabic = false.obs;

  void toggleLanguage() {
    isArabic.value = !isArabic.value;

    if (isArabic.value) {
      Get.context?.setLocale(AppLocalization.ar.locale);
      Get.updateLocale(AppLocalization.ar.locale);
    } else {
      Get.context?.setLocale(AppLocalization.en.locale);
      Get.updateLocale(AppLocalization.en.locale);
    }
  }
}
