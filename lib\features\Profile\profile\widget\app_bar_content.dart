import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/routes/routes.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/core/widgets/image.dart';
import 'package:renvo/features/Profile/profile/controller.dart';
import 'package:renvo/features/Profile/profile/model/ProfileModel.dart';
import 'package:renvo/features/home/<USER>';
import 'package:renvo/gen/assets.gen.dart';

class AppBarContent extends StatelessWidget {
  final ProfileModel Profile;
  final ProfilePageController controller;
  const AppBarContent(
      {super.key, required this.Profile, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
              physics: NeverScrollableScrollPhysics(),
              child: Padding(
                  padding: const EdgeInsets.only(top: 40),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      FormField<String>(
                        initialValue: Profile.avatar.originalUrl,
                        builder: (state) {
                          return Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Stack(
                                //  alignment: Alignment.center,
                                children: [
                                  controller.image.value.isNotEmpty
                                      ? AppImage(
                                          height: 140,
                                          width: 140,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                          ),
                                          path: controller.image.value,
                                          type: ImageType.File,
                                          fit: BoxFit.cover,
                                        )
                                      : AppImage(
                                          path: Profile.avatar.originalUrl,
                                          type: ImageType.CachedNetwork,
                                          height: 140,
                                          width: 140,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                  Positioned(
                                    bottom: 0,
                                    right: 0,
                                    child: TextButton(
                                      onPressed: () async {
                                        await controller.pickImage();
                                        state.didChange(controller.image.value);
                                        controller.image.refresh();
                                      },
                                      style: TextButton.styleFrom(
                                        padding: EdgeInsets.zero,
                                        shape: const CircleBorder(),
                                      ),
                                      child: Container(
                                        width: 41,
                                        height: 41,
                                        decoration: const BoxDecoration(
                                          color: StyleRepo.white,
                                          shape: BoxShape.circle,
                                        ),
                                        child: Assets.icons.camera.svg(),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          );
                        },
                      ),
                      const SizedBox(height: 10),
                      Text(
                        Profile.firstName,
                        style: Theme.of(context)
                            .textTheme
                            .titleSmall!
                            .copyWith(color: StyleRepo.blue),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        Profile.phone,
                        style: Theme.of(context)
                            .textTheme
                            .titleSmall!
                            .copyWith(color: StyleRepo.white),
                      ),
                      TextButton(
                        onPressed: () => Get.offNamed(Pages.Edit_profile.value,
                            arguments: controller.Profile.value!.id),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(tr(LocaleKeys.Edit_profile),
                                style: Theme.of(context)
                                    .textTheme
                                    .titleSmall!
                                    .copyWith(color: StyleRepo.white)),
                            const SizedBox(width: 5),
                            Assets.icons.arrowRight.svg(),
                          ],
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Column(
                            children: [
                              Text(
                                Profile.ordersCnt.toString(),
                                style: Theme.of(context)
                                    .textTheme
                                    .headlineMedium!
                                    .copyWith(color: StyleRepo.white),
                              ),
                              Text(
                                tr(LocaleKeys.orders),
                                style: Theme.of(context)
                                    .textTheme
                                    .titleSmall!
                                    .copyWith(color: StyleRepo.white),
                              )
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                Profile.points.toString(),
                                style: Theme.of(context)
                                    .textTheme
                                    .headlineMedium!
                                    .copyWith(color: StyleRepo.white),
                              ),
                              Text(
                                tr(LocaleKeys.Points),
                                style: Theme.of(context)
                                    .textTheme
                                    .titleSmall!
                                    .copyWith(color: StyleRepo.white),
                              )
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                Profile.pointsInSp.toString(),
                                style: Theme.of(context)
                                    .textTheme
                                    .headlineMedium!
                                    .copyWith(color: StyleRepo.white),
                              ),
                              Text(
                                tr(LocaleKeys.orders),
                                style: Theme.of(context)
                                    .textTheme
                                    .titleSmall!
                                    .copyWith(color: StyleRepo.white),
                              )
                            ],
                          )
                        ],
                      )
                    ],
                  )));
        },
      ),
    );
  }
}
