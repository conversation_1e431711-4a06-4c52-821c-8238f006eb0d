import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/services/state_management/widgets/obs_widget.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/features/home/<USER>';
import 'package:renvo/features/home/<USER>/HomeCategories.dart';
import 'package:renvo/features/home/<USER>/HomeStories.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(HomePageController());
    return Container(
      color: StyleRepo.whiteblue,
      child: CustomScrollView(
        slivers: [
          Homecategories(),
          // ObsListBuilder(
          //   obs: controller.Category,
          //   builder: (context, Category) {
          //     return Homecategories(
          //       cats: Category,
          //     );
          //   },
          // ),
          Homestories(),
        ],
      ),
    );
  }
}
