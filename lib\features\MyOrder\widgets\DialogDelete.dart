import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/gen/assets.gen.dart';

class Dialogdelete extends StatelessWidget {
  const Dialogdelete({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: StyleRepo.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      contentPadding: const EdgeInsets.all(24),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Assets.icons.deleteOrder.svg(height: 107, width: 107),
          Text(
            tr(LocaleKeys.AreYouSure),
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.titleSmall,
          ),
          SizedBox(
            height: 45,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: () {
                  Get.back();
                },
                style: ElevatedButton.styleFrom(
                  fixedSize: Size(120, 40),
                  backgroundColor: Color(0xFFEFEFFB),
                  //  foregroundColor: StyleRepo.blue_gray,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20)),
                ),
                child: Text(tr(LocaleKeys.Cancel),
                    style: Theme.of(context)
                        .textTheme
                        .labelSmall!
                        .copyWith(color: StyleRepo.blue_gray)),
              ),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: StyleRepo.pink,
                  //foregroundColor: StyleRepo.meduimred,
                  fixedSize: Size(120, 40),
                  side: BorderSide(
                    color: StyleRepo.meduimred,
                    width: 1,
                  ),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20)),
                ),
                child: Text(tr(LocaleKeys.Delete),
                    style: Theme.of(context)
                        .textTheme
                        .labelSmall!
                        .copyWith(color: StyleRepo.meduimred)),
              )
            ],
          )
        ],
      ),
    );
  }
}
