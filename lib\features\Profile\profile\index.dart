import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/services/state_management/widgets/obs_widget.dart';
import 'package:renvo/core/widgets/image.dart';
import 'package:renvo/features/Profile/profile/controller.dart';
import 'package:renvo/features/Profile/profile/widget/app_bar_content.dart';
import 'package:renvo/features/Profile/profile/widget/body_page.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ProfilePageController());

    return ObsVariableBuilder(
      obs: controller.Profile,
      builder: (context, Profile) {
        return Stack(
          children: [
            Positioned.fill(
              child: controller.image.value.isNotEmpty
                  ? ImageFiltered(
                      imageFilter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                      child: AppImage(
                        path: controller.image.value,
                        type: ImageType.File,
                        fit: BoxFit.cover,
                      ),
                    )
                  : ImageFiltered(
                      imageFilter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                      child: AppImage(
                        path: Profile.avatar.originalUrl.toString(),
                        type: ImageType.CachedNetwork,
                        fit: BoxFit.cover,
                      ),
                    ),
            ),
            ListView(
              children: [
                AppBarContent(
                  controller: controller,
                  Profile: Profile,
                ),
                const BodyPage(),
              ],
            ),
          ],
        );
      },
    );
  }
}
