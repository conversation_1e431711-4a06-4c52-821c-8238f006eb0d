import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:renvo/core/models/category.dart';
import 'package:renvo/core/services/rest_api/api_service.dart';
import 'package:renvo/core/services/rest_api/constants/end_points.dart';
import 'package:renvo/core/services/rest_api/models/request.dart';
import 'package:renvo/core/services/rest_api/models/response_model.dart';
import 'package:renvo/core/services/state_management/obs.dart';

class HomePageController extends GetxController {
  ObsList<CategoriesModel> Category = ObsList([]);

  @override
  onInit() {
    fetchCategory();
    super.onInit();
  }

  void fetchCategory() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.categories,
        fromJson: CategoriesModel.fromJson,
      ),
    );
    if (response.success) {
      Category.value = response.data;
    } else {
      Category.error = response.errorType;
    }
  }

  // @override
  // onClose() {
  //   super.onClose();
  // }
}
