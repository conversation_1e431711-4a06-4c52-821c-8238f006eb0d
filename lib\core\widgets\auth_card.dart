import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/gen/assets.gen.dart';

class AuthCard extends StatelessWidget {
  final Widget card;
  final bool leading;

  const AuthCard({super.key, required this.card, required this.leading});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            StyleRepo.blue,
            StyleRepo.whiteblue,
          ],
          begin: Alignment.topLeft,
          end: Alignment.topRight,
        ),
      ),
      child: Scaffold(
        backgroundColor: StyleRepo.transparent,
        appBar: AppBar(
          backgroundColor: StyleRepo.transparent,
          toolbarHeight: MediaQuery.of(context).size.height * 0.15,
          centerTitle: true,
          automaticallyImplyLeading: false,
          leadingWidth: 100,
          leading: leading
              ? GestureDetector(
                  onTap: Get.back,
                  child: Row(
                    children: [
                      SizedBox(
                        width: 6,
                      ),
                      Assets.icons.back.svg(),
                      SizedBox(
                        width: 10,
                      ),
                      Expanded(
                        child: Text(tr(LocaleKeys.Back),
                            style: Theme.of(context)
                                .textTheme
                                .titleSmall!
                                .copyWith(color: StyleRepo.white)),
                      )
                    ],
                  ),
                )
              : null,
          title: Assets.icons.logo
              .svg(color: StyleRepo.white, width: 70, height: 70),
          elevation: 0,
        ),
        body: Container(
            decoration: BoxDecoration(
              color: StyleRepo.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40),
                topRight: Radius.circular(40),
              ),
            ),
            child: Stack(children: [Assets.icons.authBackgraound.svg(), card])),
      ),
    );
  }
}
