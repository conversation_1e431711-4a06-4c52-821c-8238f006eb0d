import 'package:flutter/material.dart';

class StyleRepo {
  static const green = Color(0xFF218A23);
  static const indigo = Colors.indigo;
  static const transparent = Colors.transparent;
  static const white = Color(0xFFFCFCFC);
  static const lightGrey = Color(0xFFB1B1B1);
  static const lightGrey2 = Color(0xFFECECEC);
  static const darkGrey = Color(0xFF7C7C7C);
  static const mediumGrey = Color(0xFFF3F3F3);
  static const black = Color(0xFF030303);
  static const blue = Color(0xFF003399);
  static const gray = Color(0xFFA4A4A4);
  static const bluemedium = Color.fromARGB(255, 0, 54, 161);
  static const blue_gray = Color(0xFF57597E);
  static const red = Colors.red;
  static const pink = Color(0xFFFFD3D3);
  static const darkred = Color(0xFFB51A1E);
  static const meduimred = Color(0xFF992534);
  static const yellow = Colors.amber;
  static const whiteblue = Color(0xFF0048D9);
  static const sandyBrown = Color(0xFFFFC700);
  static const whiteSmoke = Color(0xFFFAFAFA);
}
