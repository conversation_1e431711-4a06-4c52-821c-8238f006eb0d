import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:image_picker/image_picker.dart';
import 'package:renvo/core/routes/routes.dart';

import '../../../core/services/rest_api/rest_api.dart';

class CompleteInformationPageController extends GetxController {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController email,
      First_name,
      Last_name,
      gender,
      National_number;
  Rx<String> profileImage = "".obs; // صورة الملف الشخصي
  Rx<String> idImage = "".obs;
  late int? id = 0;
  var isImageErrorVisible = false.obs;

  pickImage(String imageType) async {
    final picker = ImagePicker();
    XFile? picked = await picker.pickImage(source: ImageSource.gallery);
    if (picked == null) return; // إذا لم يتم اختيار صورة، لا تفعل شيئًا
    if (imageType == 'profile') {
      profileImage.value = picked.path; // تخزين صورة الملف الشخصي
    } else if (imageType == 'id') {
      idImage.value = picked.path; // تخزين صورة الهوية
    }
  }

  @override
  onInit() {
    email = TextEditingController();
    First_name = TextEditingController();
    Last_name = TextEditingController();
    National_number = TextEditingController();
    gender = TextEditingController();
    id = Get.arguments;
    super.onInit();
  }

  @override
  onClose() {
    email.dispose();
    First_name.dispose();
    Last_name.dispose();
    National_number.dispose();
    gender.dispose();
    super.onClose();
  }

  void confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.Update_profile,
        method: RequestMethod.Post,
        body: FormData.fromMap(
          {
            "id": id,
            "first_name": First_name.text,
            "last_name": Last_name.text,
            "nationalID": National_number.text,
            "gender_id": gender.text,
            "email": email.text,
            "dial_country_code": "963",
            // "avatar": await MultipartFile.fromFile(profileImage.value),
            // "IDcard": await MultipartFile.fromFile(
            //   idImage.value,
            // ),
          },
        ),
      ),
    );
    Dio dio = Dio();
    dio.interceptors.add(LogInterceptor(responseBody: true, requestBody: true));
    if (response.success) {
      Get.offAllNamed(Pages.home.value);
    } else {}
  }
}
