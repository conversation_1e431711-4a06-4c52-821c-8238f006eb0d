import 'dart:async';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:renvo/core/config/app_builder.dart';
import 'package:renvo/core/config/role.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/routes/routes.dart';

import '../../../core/services/rest_api/rest_api.dart';

class VerifyPageController extends GetxController {
  AppBuilder appBuilder = Get.find();
  RxInt secondsLeft = 60.obs;
  RxBool isResendActive = false.obs;
  late final String phoneNumber;
  late final String isforget;
  late TextEditingController otp;
  Timer? _timer;

  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  void startTimer() {
    secondsLeft.value = 60;
    isResendActive.value = false;

    _timer?.cancel(); // إلغاء المؤقت السابق إن وجد
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (secondsLeft.value == 0) {
        isResendActive.value = true;
        timer.cancel();
      } else {
        secondsLeft.value--;
      }
    });
  }

  String get formattedTime {
    final minutes = (secondsLeft.value ~/ 60).toString().padLeft(2, '0');
    final secs = (secondsLeft.value % 60).toString().padLeft(2, '0');
    return '$minutes : $secs';
  }

  @override
  void onClose() {
    _timer?.cancel();
    otp.dispose;
    super.onClose();
  }

  @override
  onInit() {
    final arguments = Get.arguments as Map<String, dynamic>;
    phoneNumber = arguments['phone'];
    isforget = arguments['isforget'];
    otp = TextEditingController();
    startTimer();
    super.onInit();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.Verify,
        method: RequestMethod.Post,
        body: {
          "otp": otp.text,
          "dial_country_code": "963",
          "phone": phoneNumber,
          "device_token": "55555",
        },
      ),
    );

    if (response.success) {
      appBuilder.setRole(Role.user);
      appBuilder.setToken(response.data['token']);
      if (isforget == "yes") {
        Get.toNamed(Pages.ResendPass.value, arguments: response.data["phone"]);
      } else {
        Get.toNamed(Pages.complete.value, arguments: response.data["id"]);
      }
    } else {
      Get.snackbar(tr(LocaleKeys.Error_Otp), tr(LocaleKeys.This_code_is_wrong));
    }
  }
}
