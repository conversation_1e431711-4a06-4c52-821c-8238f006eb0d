import 'dart:convert';

class CategoriesModel {
  int id;
  String title;
  String? svg;
  int minPrice;
  int maxPrice;
  String? description;
  List<dynamic> subCategories;
  int prvCnt;
  List<dynamic> keywords;

  CategoriesModel({
    required this.id,
    required this.title,
    this.svg,
    required this.minPrice,
    required this.maxPrice,
    this.description,
    required this.subCategories,
    required this.prvCnt,
    required this.keywords,
  });

  factory CategoriesModel.fromRawJson(String str) =>
      CategoriesModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CategoriesModel.fromJson(Map<String, dynamic> json) =>
      CategoriesModel(
        id: json["id"],
        title: json["title"],
        svg: json["svg"],
        minPrice: json["min_price"],
        maxPrice: json["max_price"],
        description: json["description"],
        subCategories: List<dynamic>.from(json["subCategories"].map((x) => x)),
        prvCnt: json["prv_cnt"],
        keywords: List<dynamic>.from(json["keywords"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "svg": svg,
        "min_price": minPrice,
        "max_price": maxPrice,
        "description": description,
        "subCategories": List<dynamic>.from(subCategories.map((x) => x)),
        "prv_cnt": prvCnt,
        "keywords": List<dynamic>.from(keywords.map((x) => x)),
      };
}
