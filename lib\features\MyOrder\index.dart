import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/config/StatusOrder.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/features/MyOrder/controller.dart';
import 'package:renvo/features/MyOrder/widgets/CardOrder.dart';
import 'package:renvo/features/MyOrder/widgets/SwipeableCard.dart';
import 'package:renvo/gen/assets.gen.dart';

class MyOrderPage extends StatelessWidget {
  const MyOrderPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(MyOrderPageController());

    final double tabBarViewHeight = MediaQuery.of(context).size.height * .62;

    return Container(
      margin: EdgeInsets.symmetric(vertical: 15),
      child: ListView(
        padding: EdgeInsets.symmetric(horizontal: 15),
        children: [
          SizedBox(height: MediaQuery.of(context).size.height * 0.05),
          Text(
            tr(LocaleKeys.MyOrder),
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: 20),
          TextFormField(
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              prefixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(width: 8),
                  Assets.icons.search.svg(),
                  SizedBox(width: 8),
                  Assets.icons.line.svg(),
                  SizedBox(width: 8),
                ],
              ),
              hintText: "Bilar , mobler , boene, mobler",
              hintStyle: Theme.of(context)
                  .textTheme
                  .labelSmall!
                  .copyWith(color: StyleRepo.darkGrey),
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(vertical: 12),
            child: TabBar(
              controller: controller.tabController,
              isScrollable: true,
              tabs: StatusOrder.values
                  .map((status) => Tab(text: status.trValue))
                  .toList(),
            ),
          ),
          SizedBox(
            height: tabBarViewHeight,
            child: TabBarView(
              controller: controller.tabController,
              children: StatusOrder.values.map((status) {
                return ListView.builder(
                  itemCount: 10,
                  itemBuilder: (context, index) {
                    return SwipeableCard(
                      onDelete: () {},
                      child: OrderCard(cardtype: status),
                    );
                  },
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
