import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/config/app_builder.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/routes/routes.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/core/widgets/image.dart';
import 'package:renvo/gen/assets.gen.dart';

class BodyPage extends StatelessWidget {
  const BodyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AppBuilder>();
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: StyleRepo.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(32),
          topRight: Radius.circular(32),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Container(
                  width: 183,
                  height: 146,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: StyleRepo.mediumGrey,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(tr(LocaleKeys.Loyalty_Points),
                          style: Theme.of(context).textTheme.labelLarge),
                      AppImage(
                          path: Assets.icons.loaytlypoint.path,
                          type: ImageType.Asset),
                    ],
                  ),
                ),
              ),
              SizedBox(
                width: 10,
              ),
              Expanded(
                child: Container(
                  width: 183,
                  height: 146,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: StyleRepo.mediumGrey,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(tr(LocaleKeys.Payment),
                          style: Theme.of(context).textTheme.labelLarge),
                      AppImage(
                          path: Assets.icons.payment.path,
                          type: ImageType.Asset),
                    ],
                  ),
                ),
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 30,
              ),
              Text(tr(LocaleKeys.account),
                  style: Theme.of(context).textTheme.titleMedium),
              SizedBox(
                height: 30,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Assets.icons.location.svg(),
                  SizedBox(
                    width: 10,
                  ),
                  TextButton(
                    onPressed: () {},
                    child: Text(tr(LocaleKeys.My_Location),
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(color: StyleRepo.gray)),
                  )
                ],
              ),
              SizedBox(
                height: 30,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Assets.icons.changePass.svg(),
                  SizedBox(
                    width: 10,
                  ),
                  TextButton(
                    onPressed: () {},
                    child: Text(tr(LocaleKeys.Change_Password),
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(color: StyleRepo.gray)),
                  )
                ],
              ),
              SizedBox(
                height: 30,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Assets.icons.notification.svg(),
                  SizedBox(
                    width: 10,
                  ),
                  TextButton(
                    onPressed: () {},
                    child: Text(tr(LocaleKeys.App_Notifications),
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(color: StyleRepo.gray)),
                  ),
                ],
              ),
              SizedBox(
                height: 30,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Assets.icons.privacy.svg(),
                  SizedBox(
                    width: 10,
                  ),
                  TextButton(
                    onPressed: () {},
                    child: Text(tr(LocaleKeys.privacy_policy),
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(color: StyleRepo.gray)),
                  ),
                ],
              ),
              SizedBox(
                height: 30,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Assets.icons.contact.svg(),
                  SizedBox(
                    width: 10,
                  ),
                  TextButton(
                    onPressed: () {},
                    child: Text(tr(LocaleKeys.Contact_Us),
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(color: StyleRepo.gray)),
                  ),
                ],
              ),
              SizedBox(
                height: 30,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Assets.icons.faq.svg(),
                  SizedBox(
                    width: 10,
                  ),
                  TextButton(
                    onPressed: () {},
                    child: Text(tr(LocaleKeys.FAQ),
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(color: StyleRepo.gray)),
                  ),
                ],
              ),
              SizedBox(
                height: 30,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Assets.icons.logout.svg(),
                  SizedBox(
                    width: 10,
                  ),
                  TextButton(
                    onPressed: () {
                      controller.logout();
                      Get.offAllNamed(Pages.login.value);
                    },
                    child: Text(tr(LocaleKeys.Logout),
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(color: StyleRepo.red)),
                  ),
                ],
              ),
              SizedBox(
                height: 30,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Assets.icons.delete.svg(),
                  SizedBox(
                    width: 10,
                  ),
                  TextButton(
                    onPressed: () {},
                    child: Text(tr(LocaleKeys.Delete_my_account),
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(color: StyleRepo.red)),
                  ),
                ],
              ),
              SizedBox(
                height: 100,
              ),
            ],
          )
        ],
      ),
    );
  }
}
