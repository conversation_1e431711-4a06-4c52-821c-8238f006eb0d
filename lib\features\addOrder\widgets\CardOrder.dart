import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/gen/assets.gen.dart';

class CardOrder extends StatelessWidget {
  final String title;
  final String subtitle;
  final SvgPicture picture;

  const CardOrder({
    super.key,
    required this.title,
    required this.subtitle,
    required this.picture,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 25.8, sigmaY: 25.8),
        child: Container(
          height: 105,
          width: 330,
          color: Color(0x1AFFFFFF),
          child: Row(
            children: [
              Assets.icons.linewhite.svg(),
              const SizedBox(width: 5),
              picture,
              const SizedBox(width: 5),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      title,
                      style: Theme.of(context)
                          .textTheme
                          .labelLarge!
                          .copyWith(color: StyleRepo.white),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      subtitle,
                      style: Theme.of(context)
                          .textTheme
                          .labelSmall!
                          .copyWith(color: StyleRepo.white),
                    ),
                  ],
                ),
              ),
              Assets.icons.arrowRight.svg(height: 24, width: 24),
              SizedBox(
                width: 20,
              )
            ],
          ),
        ),
      ),
    );
  }
}
