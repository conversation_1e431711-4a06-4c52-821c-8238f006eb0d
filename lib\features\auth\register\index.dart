import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/widgets/auth_card.dart';
import 'package:renvo/features/auth/register/controller.dart';
import 'package:renvo/gen/assets.gen.dart';

class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(RegisterPageController());

    return AuthCard(
      leading: true,
      card: Form(
        key: controller.formKey,
        child: ListView(
          padding: EdgeInsets.all(15),
          children: [
            SizedBox(height: MediaQuery.sizeOf(context).height * .02),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(tr(LocaleKeys.Create_New_Account),
                    style: Theme.of(context).textTheme.headlineSmall),
                SizedBox(
                  width: 16,
                ),
              ],
            ),
            Text(tr(LocaleKeys.add_your_information),
                style: Theme.of(context).textTheme.bodyMedium),
            Sized<PERSON>ox(
              height: 35,
            ),
            Text(tr(LocaleKeys.Phone_Number),
                style: Theme.of(context).textTheme.bodyMedium),
            const SizedBox(height: 6),
            TextFormField(
              keyboardType: TextInputType.phone,
              controller: controller.phoneNumber,
              decoration: InputDecoration(
                prefixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(width: 8),
                    Assets.icons.call.svg(),
                    SizedBox(width: 8),
                    Assets.icons.line.svg(),
                    SizedBox(width: 8),
                  ],
                ),
                hintText: tr(LocaleKeys.Ex),
              ),
              validator: (value) {
                if (value!.isEmpty) {
                  return tr(LocaleKeys.This_field_is_required);
                }
                // if (!value.contains("+963")) {
                //   return tr(LocaleKeys.Wrong_phone);
                // }
                return null;
              },
            ),
            SizedBox(height: 30),
            Text(tr(LocaleKeys.Password),
                style: Theme.of(context).textTheme.bodyMedium),
            const SizedBox(height: 6),
            TextFormField(
              controller: controller.password,
              decoration: InputDecoration(
                prefixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(width: 8),
                    Assets.icons.key.svg(),
                    SizedBox(width: 8),
                    Assets.icons.line.svg(),
                    SizedBox(width: 8),
                  ],
                ),
                hintText: tr(LocaleKeys.add_strong_password),
              ),
              validator: (value) {
                if (value!.isEmpty) {
                  return tr(LocaleKeys.This_field_is_required);
                }
                if (!controller.passwordRegex.hasMatch(value)) {
                  return tr(LocaleKeys.Password_must_be);
                }

                return null;
              },
            ),
            SizedBox(height: 30),
            Text(tr(LocaleKeys.Confirm_Password),
                style: Theme.of(context).textTheme.bodyMedium),
            const SizedBox(height: 6),
            TextFormField(
              controller: controller.Confirm_Password,
              decoration: InputDecoration(
                prefixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(width: 8),
                    Assets.icons.key.svg(),
                    SizedBox(width: 8),
                    Assets.icons.line.svg(),
                    SizedBox(width: 8),
                  ],
                ),
                hintText: tr(LocaleKeys.Confirm_Password),
              ),
              validator: (value) {
                if (controller.password.text != value || value!.isEmpty) {
                  return tr(LocaleKeys.Passwords_do_not_match);
                }

                return null;
              },
            ),
            SizedBox(height: 100),
            Center(
              child: ElevatedButton(
                onPressed: controller.confirm,
                child: Text(
                  tr(LocaleKeys.Confirm),
                ),
              ),
            ),
            Obx(() {
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Switch(
                    value: controller.isArabic.value,
                    onChanged: (bool value) {
                      controller.toggleLanguage();
                    },
                  ),
                  SizedBox(height: 20),
                  Text(
                    tr(LocaleKeys.Change_language),
                    textAlign: TextAlign.center,
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }
}
