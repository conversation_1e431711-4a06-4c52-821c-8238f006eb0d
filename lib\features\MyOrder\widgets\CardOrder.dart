import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/config/StatusOrder.dart';
import 'package:renvo/core/localization/strings.dart';
import 'package:renvo/core/style/repo.dart';
import 'package:renvo/features/MyOrder/controller.dart';
import 'package:renvo/features/MyOrder/widgets/PopUp.dart';
import 'package:renvo/gen/assets.gen.dart';

class OrderCard extends StatelessWidget {
  final StatusOrder cardtype;

  const OrderCard({super.key, required this.cardtype});

  @override
  Widget build(BuildContext context) {
    final Controller = Get.find<MyOrderPageController>();
    return Card(
      color: StyleRepo.lightGrey2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      child: Column(
        children: [
          SizedBox(
            height: 37,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "${tr(LocaleKeys.ID)} 32UD457",
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Text(
                  "21 NOV at 03:00 PM ",
                  style: Theme.of(context).textTheme.labelSmall,
                ),
                if (cardtype == StatusOrder.Complete ||
                    cardtype == StatusOrder.Cancelled)
                  Row(
                    children: [
                      Assets.icons.eye.svg(),
                      SizedBox(
                        width: 2,
                      ),
                      Popup(
                        icon: Assets.icons.view.svg(),
                      )
                    ],
                  )
                else
                  Popup(
                    icon: Assets.icons.a3points.svg(),
                  )
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.only(right: 6, left: 6, bottom: 6),
            decoration: BoxDecoration(
              color: StyleRepo.white,
              borderRadius: BorderRadius.circular(24),
            ),
            padding: const EdgeInsets.all(5),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Assets.icons.iconorder.svg(),
                    SizedBox(
                      width: 15,
                    ),
                    Column(
                      children: [
                        Text(
                          "Services Category",
                          style: Theme.of(context).textTheme.labelLarge,
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        Text(
                          "Services Subcategory",
                          style: Theme.of(context)
                              .textTheme
                              .labelSmall!
                              .copyWith(color: StyleRepo.darkGrey),
                        ),
                      ],
                    ),
                  ],
                ),
                const Divider(),
                const SizedBox(
                  width: 12,
                ),
                Text(
                  "Here we  write the services title - Here we  write the services title .... ",
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.labelMedium,
                ),
                const Divider(),
                Row(
                  children: [
                    const SizedBox(
                      width: 12,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.location_pin,
                                color: StyleRepo.darkGrey,
                              ),
                              Expanded(
                                child: Text(
                                  "Örbyhus - ICA Supermarket",
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelLarge!
                                      .copyWith(color: StyleRepo.darkGrey),
                                ),
                              ),
                            ],
                          ),
                          Divider(),
                          if (cardtype == StatusOrder.Pending)
                            IconButton(
                              onPressed: () {},
                              icon: Text(
                                tr(LocaleKeys.View),
                                style: Theme.of(context)
                                    .textTheme
                                    .labelSmall!
                                    .copyWith(color: StyleRepo.blue),
                              ),
                            )
                          else if (cardtype == StatusOrder.Underway)
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "name of services provider",
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelSmall!
                                      .copyWith(color: StyleRepo.darkGrey),
                                ),
                                SizedBox(width: 8),
                                IconButton(
                                  onPressed: () {},
                                  icon: Text(
                                    tr(LocaleKeys.View_offer),
                                    style: Theme.of(context)
                                        .textTheme
                                        .labelMedium!
                                        .copyWith(
                                          color: StyleRepo.blue,
                                          decoration: TextDecoration.underline,
                                        ),
                                  ),
                                )
                              ],
                            )
                          else if (cardtype == StatusOrder.Complete)
                            Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      tr(LocaleKeys.Rating_Review),
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleSmall!
                                          .copyWith(color: StyleRepo.black),
                                    ),
                                    SizedBox(width: 60),
                                    Image.asset(
                                      Assets.icons.profilepng.path,
                                      width: 24,
                                      height: 24,
                                    ),
                                    Expanded(
                                      child: Text(
                                        "name of services provider",
                                        maxLines: 1,
                                        style: Theme.of(context)
                                            .textTheme
                                            .labelSmall!
                                            .copyWith(
                                              color: StyleRepo.darkGrey,
                                            ),
                                      ),
                                    )
                                  ],
                                ),
                                SizedBox(
                                  height: 17,
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      tr(LocaleKeys.Top_to_rating),
                                      maxLines: 1,
                                      style: Theme.of(context)
                                          .textTheme
                                          .labelSmall!
                                          .copyWith(
                                            color: StyleRepo.green,
                                            decoration:
                                                TextDecoration.underline,
                                          ),
                                    ),
                                    SizedBox(width: 35),
                                    Obx(() => Row(
                                          children: List.generate(5, (index) {
                                            return GestureDetector(
                                              onTap: () => Controller.setRating(
                                                  index + 1),
                                              child: Icon(
                                                index < Controller.rating.value
                                                    ? Icons.star
                                                    : Icons.star_border,
                                                color: index <
                                                        Controller.rating.value
                                                    ? StyleRepo.sandyBrown
                                                    : StyleRepo.lightGrey,
                                                size: 24,
                                              ),
                                            );
                                          }),
                                        ))
                                  ],
                                )
                              ],
                            )
                          else if (cardtype == StatusOrder.Cancelled)
                            Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      tr(LocaleKeys.Cancelesd_by),
                                      style: Theme.of(context)
                                          .textTheme
                                          .labelMedium!
                                          .copyWith(color: StyleRepo.red),
                                    ),
                                    SizedBox(width: 40),
                                    Image.asset(
                                      Assets.icons.profilepng.path,
                                      width: 24,
                                      height: 24,
                                    ),
                                    Expanded(
                                      child: Text(
                                        "name of services provider",
                                        maxLines: 1,
                                        style: Theme.of(context)
                                            .textTheme
                                            .labelSmall!
                                            .copyWith(
                                              color: StyleRepo.darkGrey,
                                            ),
                                      ),
                                    )
                                  ],
                                ),
                                SizedBox(
                                  height: 17,
                                ),
                                Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        tr(LocaleKeys.Reason_of_cancelation),
                                        maxLines: 1,
                                        style: Theme.of(context)
                                            .textTheme
                                            .labelSmall!
                                            .copyWith(
                                              color: StyleRepo.darkGrey,
                                            ),
                                      ),
                                      SizedBox(width: 30),
                                      Expanded(
                                        child: Text(
                                          "21 NOV at 03:00 PM ",
                                          maxLines: 1,
                                          style: Theme.of(context)
                                              .textTheme
                                              .labelSmall!
                                              .copyWith(color: StyleRepo.red),
                                        ),
                                      ),
                                    ])
                              ],
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
