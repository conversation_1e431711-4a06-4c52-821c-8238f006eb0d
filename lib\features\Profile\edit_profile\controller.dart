import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:image_picker/image_picker.dart';
import 'package:renvo/core/routes/routes.dart';
import 'package:renvo/core/services/state_management/obs.dart';
import 'package:renvo/features/Profile/profile/model/ProfileModel.dart';

import '../../../core/services/rest_api/rest_api.dart';

class EditProfilePageController extends GetxController {
  late int? id = 0;
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  Rx<String> image = "".obs;

  pickImage() async {
    XFile? picked = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (picked == null) return;

    image.value = picked.path;
  }

  late TextEditingController email,
      First_name,
      Last_name,
      gender,
      phoneNumber,
      National_number;
  @override
  onInit() {
    email = TextEditingController();
    First_name = TextEditingController();
    Last_name = TextEditingController();
    National_number = TextEditingController();
    gender = TextEditingController();
    phoneNumber = TextEditingController();
    id = Get.arguments;
    fetchProfile();
    super.onInit();
  }

  @override
  onClose() {
    email.dispose();
    First_name.dispose();
    Last_name.dispose();
    National_number.dispose();
    gender.dispose();
    super.onClose();
  }

  void confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.Update_profile,
        method: RequestMethod.Post,
        body: FormData.fromMap(
          {
            "id": id,
            "first_name": First_name.text,
            "last_name": Last_name.text,
            "nationalID": National_number.text,
            "gender_id": gender.text,
            "phone": "955",
            "dial_country_code": "963",
            "email": email.text,
            //"avatar": await MultipartFile.fromFile(image.value),
          },
        ),
      ),
    );
    // المفروض يشمل ملف الصورة
    print(FormData);
    if (response.success) {
      Get.toNamed(Pages.home.value);
    } else {}
  }
  /****************************** */

  ObsVar<ProfileModel> Profile = ObsVar(null);

  void fetchProfile() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.Profile,
        fromJson: ProfileModel.fromJson,
      ),
    );

    if (response.success) {
      Profile.value = response.data;
      First_name.text = Profile.value!.firstName;
      Last_name.text = Profile.value!.lastName;
      email.text = Profile.value!.email.toString();
      National_number.text = Profile.value!.nationalId;
      phoneNumber.text = Profile.value!.phone;
      gender.text = Profile.value!.gender.name;
      image.value = Profile.value!.avatar.originalUrl;
    } else {
      Profile.error = response.message;
    }
  }
}
