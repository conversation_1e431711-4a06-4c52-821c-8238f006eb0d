import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo/core/config/app_builder.dart';
import 'package:renvo/core/config/role.dart';
import 'package:renvo/core/routes/routes.dart';
import 'package:renvo/core/services/rest_api/rest_api.dart';

class LoginPageController extends GetxController {
  AppBuilder appBuilder = Get.find();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController phoneNumber, password;
  var passwordRegex = RegExp(
    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$',
  );
  @override
  onInit() {
    phoneNumber = TextEditingController();
    password = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    phoneNumber.dispose();
    password.dispose();
    super.onClose();
  }

  forgetPassword() async {
    if (phoneNumber.text.isEmpty) {
      Get.snackbar("Error input", "set phone please");
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.ForgetPass,
        method: RequestMethod.Post,
        body: {
          "phone": phoneNumber.text,
          "dial_country_code": "963",
        },
      ),
    );

    if (response.success) {
      Get.toNamed(Pages.verify.value, arguments: {
        "phone": phoneNumber.text,
        "isforget": "yes",
      });
    }
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.login,
        method: RequestMethod.Post,
        body: {
          "phone": phoneNumber.text,
          "dial_country_code": "963",
          "password": password.text,
          "device_token": "2125"
        },
      ),
    );

    if (response.success) {
      if (response.data is List) {
        Get.toNamed(Pages.verify.value, arguments: {
          "phone": phoneNumber.text,
          "isforget": "no",
        });
      } else {
        appBuilder.setRole(Role.user);
        appBuilder.setToken(response.data['token']);
        if (response.data['is_completed'] == 0) {
          Get.offAllNamed(
            Pages.complete.value,
          );
        } else {
          Get.offNamed(
            Pages.home.value,
          );
        }
      }
    } else {
      Get.snackbar("Errore Auth", "User not found");
    }
  }
}
